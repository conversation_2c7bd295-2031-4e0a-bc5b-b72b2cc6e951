# 井云交互数字人部署指南

## 问题排查：404 Not Found 错误

当访问 `https://jiaohu.jingyuncenter.com/config` 时出现 404 错误，是因为 Nginx 服务器尝试查找物理路径 `/www/wwwroot/szr.jingyuncenter.com/config`，但这个路径不存在。这是因为 `/config` 是 React Router 的前端路由，而不是实际的文件路径。

## 解决方案

### 1. 修改 Nginx 配置

需要修改 Nginx 配置，将所有不存在的路径请求都重定向到 `index.html`，让 React Router 处理前端路由。

将 `nginx.conf.example` 文件中的配置应用到您的 Nginx 配置中。主要关注以下部分：

```nginx
# 处理所有其他请求，重定向到 index.html 以支持 React Router
location / {
    try_files $uri $uri/ /index.html;
}
```

### 2. 配置说明

- **静态资源处理**：为 JS、CSS、图片等静态资源设置了缓存策略
- **API 代理**：将 `/api` 请求代理到后端服务器
- **上传文件处理**：配置了 `/uploads` 路径的处理
- **前端路由支持**：使用 `try_files` 指令将所有不存在的路径请求重定向到 `index.html`

### 3. 应用配置

1. 根据您的实际情况修改 `nginx.conf.example` 文件中的路径和域名
2. 将配置应用到 Nginx：
   ```bash
   # 检查配置是否有语法错误
   sudo nginx -t
   
   # 如果配置正确，重新加载 Nginx
   sudo nginx -s reload
   ```

### 4. 验证配置

配置应用后，访问以下 URL 应该都能正常工作：
- `https://jiaohu.jingyuncenter.com/`
- `https://jiaohu.jingyuncenter.com/config`
- `https://jiaohu.jingyuncenter.com/任意路径`

## 其他可能的解决方案

如果无法修改 Nginx 配置，可以考虑以下替代方案：

### 1. 使用 HashRouter

修改 `src/App.tsx` 文件，将 `BrowserRouter` 替换为 `HashRouter`：

```jsx
import { HashRouter as Router, Routes, Route } from 'react-router-dom';
```

这样 URL 会变成 `/#/config` 而不是 `/config`，避免服务器查找不存在的路径。

### 2. 创建静态页面

在构建目录中创建 `config/index.html` 文件，内容与根目录的 `index.html` 相同，但这种方法不推荐，因为需要为每个路由创建对应的静态文件。 