import json
import os

def read_figures_from_tmp():
    """
    从tmp.json中读取数字人列表
    
    Returns:
        list: 数字人名称列表
    """
    tmp_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'tmp.json')
    print(f"正在读取文件: {tmp_path}")
    
    try:
        with open(tmp_path, 'r', encoding='utf-8') as f:
            content = f.read()
            print(f"文件内容长度: {len(content)} 字符")
            tmp_data = json.loads(content)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {tmp_path}")
        return []
    except json.JSONDecodeError as e:
        print(f"错误: {tmp_path} 不是有效的JSON文件")
        print(f"JSON解析错误: {str(e)}")
        return []
    except Exception as e:
        print(f"读取文件时发生未知错误: {str(e)}")
        return []

    # 从tmp.json中提取数字人名称
    figure_names = []
    try:
        figure_list = tmp_data.get('result', {}).get('figureList', [])
        print(f"找到 {len(figure_list)} 个数字人")
        for figure in figure_list:
            if figure.get('name'):
                figure_names.append(figure['name'])
        print(f"成功提取 {len(figure_names)} 个数字人名称")
    except Exception as e:
        print(f"处理数字人列表时发生错误: {str(e)}")
        return []
    
    return figure_names

def add_tag_to_figures(tag_name, figure_names):
    """
    为指定的数字人添加标签
    
    Args:
        tag_name (str): 要添加的标签名称
        figure_names (list): 数字人名称列表
    """
    # 读取figures.json文件
    figures_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config', 'figures.json')
    print(f"正在读取文件: {figures_path}")
    
    try:
        with open(figures_path, 'r', encoding='utf-8') as f:
            figures_data = json.load(f)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {figures_path}")
        return
    except json.JSONDecodeError as e:
        print(f"错误: {figures_path} 不是有效的JSON文件")
        print(f"JSON解析错误: {str(e)}")
        return
    except Exception as e:
        print(f"读取文件时发生未知错误: {str(e)}")
        return

    # 遍历figureList
    updated_count = 0
    figure_list = figures_data.get('figureList', [])
    print(f"figures.json中找到 {len(figure_list)} 个数字人")
    
    for figure in figure_list:
        if figure.get('name') in figure_names:
            # 如果figure已经有tags字段，则添加新标签
            if 'tags' not in figure:
                figure['tags'] = []
            if tag_name not in figure['tags']:
                figure['tags'].append(tag_name)
                updated_count += 1
                print(f"为数字人 '{figure['name']}' 添加标签")

    # 写回文件
    try:
        with open(figures_path, 'w', encoding='utf-8') as f:
            json.dump(figures_data, f, ensure_ascii=False, indent=4)
        print(f"成功为 {updated_count} 个数字人添加标签 '{tag_name}'")
        # print("已更新的数字人:")
        # for name in figure_names:
        #     print(f"- {name}")
    except Exception as e:
        print(f"保存文件时出错: {str(e)}")

if __name__ == "__main__":
    # 从tmp.json中读取数字人列表
    figure_names = read_figures_from_tmp()
    if not figure_names:
        print("错误: 无法从tmp.json中读取数字人列表")
        exit(1)
        
    # 设置标签名称
    # tag_name = "DAILY_LEISURE_STYLE"
    tag_name = input("请输入标签名称: ")
    
    # 添加标签
    add_tag_to_figures(tag_name, figure_names) 