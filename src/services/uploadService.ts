import axios from 'axios';
import { API_BASE_URL, STATIC_BASE_URL } from '../config/constants';

export interface UploadedBackground {
  id: string;
  url: string;
  type: 'image' | 'video';
  name: string;
}

export class UploadService {
  private static instance: UploadService;

  private constructor() {}

  public static getInstance(): UploadService {
    if (!UploadService.instance) {
      UploadService.instance = new UploadService();
    }
    return UploadService.instance;
  }

  public async uploadBackground(file: File): Promise<{ id: string; url: string }> {
    // 检查并处理文件名长度
    let processedFile = file;
    if (file.name.length > 20) {
      const ext = file.name.split('.').pop();
      const baseName = file.name.substring(0, file.name.lastIndexOf('.'));
      const truncatedName = baseName.substring(0, 20) + '.' + ext;
      console.log(`文件名过长，从 "${file.name}" 截断为 "${truncatedName}"`);

      // 创建新的File对象，使用截断后的文件名
      processedFile = new File([file], truncatedName, { type: file.type });
    }

    const formData = new FormData();
    formData.append('file', processedFile);

    try {
      const response = await axios.post(`${API_BASE_URL}/upload-background`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        timeout: 600000, // 10分钟超时
        maxContentLength: 200 * 1024 * 1024, // 200MB
        maxBodyLength: 200 * 1024 * 1024, // 200MB
      });

      // 检查响应数据
      if (!response.data || !response.data.url) {
        throw new Error('服务器返回的数据格式不正确');
      }

      // 处理URL路径
      let url = response.data.url;
      if (!url.startsWith('http')) {
        // 如果URL以/uploads开头，直接使用静态资源基础URL
        if (url.startsWith('/uploads/')) {
          url = `${STATIC_BASE_URL}${url}`;
        } else {
          // 否则使用API基础URL
          url = `${API_BASE_URL}${url}`;
        }
      }

      return {
        id: response.data.id,
        url: url
      };
    } catch (error) {
      console.error('上传背景失败:', error);
      throw new Error('上传背景失败');
    }
  }

  public async getUploadedBackgrounds(): Promise<UploadedBackground[]> {
    try {
      const response = await axios.get(`${API_BASE_URL}/backgrounds`);

      // 确保返回的数据格式正确
      if (!Array.isArray(response.data)) {
        throw new Error('服务器返回的数据格式不正确');
      }

      // 处理每个背景的URL
      return response.data.map(item => {
        let url = item.url;
        if (!url.startsWith('http')) {
          // 如果URL以/uploads开头，直接使用静态资源基础URL
          if (url.startsWith('/uploads/')) {
            url = `${STATIC_BASE_URL}${url}`;
          } else {
            // 否则使用API基础URL
            url = `${API_BASE_URL}${url}`;
          }
        }
        return {
          ...item,
          url: url
        };
      });
    } catch (error) {
      console.error('获取背景列表失败:', error);
      throw new Error('获取背景列表失败');
    }
  }

  public async deleteBackground(id: string): Promise<void> {
    try {
      console.log('删除背景请求 - 原始ID:', id);
      const encodedId = encodeURIComponent(id);
      console.log('删除背景请求 - 编码后ID:', encodedId);
      await axios.delete(`${API_BASE_URL}/backgrounds/${encodedId}`);
    } catch (error) {
      console.error('删除背景失败:', error);
      throw new Error('删除背景失败');
    }
  }
}