import axios from 'axios';
import { API_BASE_URL } from '../config/constants';

// 知识库类型定义
export interface KnowledgeBase {
  id: string;
  name: string;
  description: string | null;
  permission: string;
  document_count: number;
  word_count: number;
  created_at: number;
  updated_at: number;
}

// 知识库文档类型定义
export interface KnowledgeDocument {
  id: string;
  name: string;
  position: number;
  indexing_status: string;
  created_at: number;
  word_count: number;
  enabled: boolean;
  display_status: string;
}

// 知识库分段类型定义
export interface KnowledgeSegment {
  id: string;
  position: number;
  document_id: string;
  content: string;
  answer?: string;
  keywords?: string[];
  word_count: number;
  enabled: boolean;
  status: string;
  created_at: number;
}

export class KnowledgeBaseService {
  private static instance: KnowledgeBaseService;
  private apiKey: string = '';
  private constructor() {}

  public static getInstance(): KnowledgeBaseService {
    if (!KnowledgeBaseService.instance) {
      KnowledgeBaseService.instance = new KnowledgeBaseService();
    }
    return KnowledgeBaseService.instance;
  }

  public setCredentials(apiKey: string): void {
    this.apiKey = apiKey;
  }

  private getHeaders() {
    return {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json'
    };
  }

  // 获取知识库列表
  public async getKnowledgeBases(page: number = 1, limit: number = 20): Promise<{
    data: KnowledgeBase[];
    total: number;
    page: number;
    has_more: boolean;
  }> {
    try {
      console.log('请求知识库列表:', `${API_BASE_URL}/knowledge-base/datasets`, { page, limit });
      const response = await axios.get(`${API_BASE_URL}/knowledge-base/datasets`, {
        params: { page, limit },
        headers: this.getHeaders()
      });
      console.log('知识库列表响应:', response.status);
      return response.data;
    } catch (error: any) {
      console.error('获取知识库列表失败:', error.message);
      if (error.response) {
        console.error('错误响应:', {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers
        });
      } else if (error.request) {
        console.error('未收到响应:', error.request);
      }
      throw new Error(`获取知识库列表失败: ${error.message}`);
    }
  }

  // 创建知识库
  public async createKnowledgeBase(name: string, permission: string = 'only_me'): Promise<KnowledgeBase> {
    try {
      const response = await axios.post(`${API_BASE_URL}/knowledge-base/datasets`, {
        name,
        permission
      }, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('创建知识库失败:', error);
      throw new Error('创建知识库失败');
    }
  }

  // 删除知识库
  public async deleteKnowledgeBase(datasetId: string): Promise<void> {
    try {
      await axios.delete(`${API_BASE_URL}/knowledge-base/datasets/${datasetId}`, {
        headers: this.getHeaders()
      });
    } catch (error) {
      console.error('删除知识库失败:', error);
      throw new Error('删除知识库失败');
    }
  }

  // 获取文档列表
  public async getDocuments(datasetId: string, page: number = 1, limit: number = 20): Promise<{
    data: KnowledgeDocument[];
    total: number;
    page: number;
    has_more: boolean;
  }> {
    try {
      const response = await axios.get(`${API_BASE_URL}/knowledge-base/datasets/${datasetId}/documents`, {
        params: { page, limit },
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('获取文档列表失败:', error);
      throw new Error('获取文档列表失败');
    }
  }

  // 通过文本创建文档
  public async createDocumentByText(datasetId: string, name: string, text: string, indexingTechnique: 'high_quality' | 'economy' = 'high_quality'): Promise<any> {
    try {
      const response = await axios.post(`${API_BASE_URL}/knowledge-base/datasets/${datasetId}/document/create_by_text`, {
        name,
        text,
        indexing_technique: indexingTechnique,
        process_rule: { mode: 'automatic' }
      }, {
        headers: this.getHeaders()
      });
      return response.data;
    } catch (error) {
      console.error('创建文档失败:', error);
      throw new Error('创建文档失败');
    }
  }

  // 上传文件创建文档
  public async createDocumentByFile(datasetId: string, file: File, indexingTechnique: 'high_quality' | 'economy' = 'high_quality'): Promise<any> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const data = JSON.stringify({
        indexing_technique: indexingTechnique,
        process_rule: {
          mode: 'automatic'
        }
      });
      formData.append('data', new Blob([data], { type: 'text/plain' }));

      const response = await axios.post(
        `${API_BASE_URL}/knowledge-base/datasets/${datasetId}/document/create_by_file`,
        formData,
        {
          headers: {
            ...this.getHeaders(),
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error('上传文件失败:', error);
      throw new Error('上传文件失败');
    }
  }

  // 删除文档
  public async deleteDocument(datasetId: string, documentId: string): Promise<any> {
    try {
      const response = await axios.delete(
        `${API_BASE_URL}/knowledge-base/datasets/${datasetId}/documents/${documentId}`,
        {
          headers: this.getHeaders()
        }
      );
      return response.data;
    } catch (error) {
      console.error('删除文档失败:', error);
      throw new Error('删除文档失败');
    }
  }

  // 获取文档分段
  public async getSegments(datasetId: string, documentId: string): Promise<{
    data: KnowledgeSegment[];
    doc_form: string;
  }> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/knowledge-base/datasets/${datasetId}/documents/${documentId}/segments`,
        {
          headers: this.getHeaders()
        }
      );
      return response.data;
    } catch (error) {
      console.error('获取文档分段失败:', error);
      throw new Error('获取文档分段失败');
    }
  }

  // 添加分段
  public async addSegments(datasetId: string, documentId: string, segments: Array<{
    content: string;
    answer?: string;
    keywords?: string[];
  }>): Promise<any> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/knowledge-base/datasets/${datasetId}/documents/${documentId}/segments`,
        { segments },
        {
          headers: this.getHeaders()
        }
      );
      return response.data;
    } catch (error) {
      console.error('添加分段失败:', error);
      throw new Error('添加分段失败');
    }
  }

  // 删除分段
  public async deleteSegment(datasetId: string, documentId: string, segmentId: string): Promise<any> {
    try {
      const response = await axios.delete(
        `${API_BASE_URL}/knowledge-base/datasets/${datasetId}/documents/${documentId}/segments/${segmentId}`,
        {
          headers: this.getHeaders()
        }
      );
      return response.data;
    } catch (error) {
      console.error('删除分段失败:', error);
      throw new Error('删除分段失败');
    }
  }

  // 更新分段
  public async updateSegment(datasetId: string, documentId: string, segmentId: string, segment: {
    content: string;
    answer?: string;
    keywords?: string[];
    enabled?: boolean;
  }): Promise<any> {
    try {
      const response = await axios.post(
        `${API_BASE_URL}/knowledge-base/datasets/${datasetId}/documents/${documentId}/segments/${segmentId}`,
        { segment },
        {
          headers: this.getHeaders()
        }
      );
      return response.data;
    } catch (error) {
      console.error('更新分段失败:', error);
      throw new Error('更新分段失败');
    }
  }

  // 获取文档嵌入状态（进度）
  public async getIndexingStatus(datasetId: string, batch: string): Promise<any> {
    try {
      const response = await axios.get(
        `${API_BASE_URL}/knowledge-base/datasets/${datasetId}/documents/${batch}/indexing-status`,
        {
          headers: this.getHeaders()
        }
      );
      return response.data;
    } catch (error) {
      console.error('获取文档嵌入状态失败:', error);
      throw new Error('获取文档嵌入状态失败');
    }
  }
}