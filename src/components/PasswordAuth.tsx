import React, { useState, useEffect } from 'react';
import { Card, Input, Button, Typography, message, Space } from 'antd';
import { LockOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';
import { ConfigService } from '../services/configService';

const { Title, Text } = Typography;

interface PasswordAuthProps {
  onSuccess: () => void;
}

const PasswordAuth: React.FC<PasswordAuthProps> = ({ onSuccess }) => {
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [correctPassword, setCorrectPassword] = useState('');

  useEffect(() => {
    loadCorrectPassword();
  }, []);

  const loadCorrectPassword = async () => {
    try {
      const configService = ConfigService.getInstance();
      const config = await configService.getConfig();
      setCorrectPassword(config.accessPassword || '');
    } catch (error) {
      console.error('加载配置失败:', error);
      message.error('加载配置失败');
    }
  };

  const handleSubmit = async () => {
    if (!password.trim()) {
      message.error('请输入密码');
      return;
    }

    setLoading(true);
    
    // 模拟验证延迟
    setTimeout(() => {
      if (password === correctPassword) {
        message.success('密码正确，正在进入...');
        // 将验证状态保存到 sessionStorage，页面刷新后需要重新验证
        sessionStorage.setItem('passwordVerified', 'true');
        onSuccess();
      } else {
        message.error('密码错误，请重试');
        setPassword('');
      }
      setLoading(false);
    }, 500);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSubmit();
    }
  };

  return (
    <div style={{
      width: '100vw',
      height: '100vh',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      position: 'fixed',
      top: 0,
      left: 0,
      zIndex: 9999
    }}>
      <Card
        style={{
          width: '400px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
          borderRadius: '16px',
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)'
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <div style={{
            width: '80px',
            height: '80px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 16px',
            boxShadow: '0 4px 16px rgba(102, 126, 234, 0.3)'
          }}>
            <LockOutlined style={{ fontSize: '32px', color: 'white' }} />
          </div>
          <Title level={3} style={{ margin: 0, color: '#333' }}>
            访问验证
          </Title>
          <Text type="secondary" style={{ fontSize: '14px' }}>
            请输入访问密码以继续
          </Text>
        </div>

        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <div>
            <Input.Password
              size="large"
              placeholder="请输入访问密码"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              onKeyPress={handleKeyPress}
              iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
              style={{
                borderRadius: '8px',
                border: '2px solid #f0f0f0',
                transition: 'all 0.3s'
              }}
              autoFocus
            />
          </div>

          <Button
            type="primary"
            size="large"
            block
            loading={loading}
            onClick={handleSubmit}
            style={{
              borderRadius: '8px',
              height: '48px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              border: 'none',
              fontSize: '16px',
              fontWeight: 'bold',
              boxShadow: '0 4px 16px rgba(102, 126, 234, 0.3)'
            }}
          >
            {loading ? '验证中...' : '确认'}
          </Button>
        </Space>

        <div style={{
          textAlign: 'center',
          marginTop: '24px',
          padding: '16px',
          background: 'rgba(102, 126, 234, 0.1)',
          borderRadius: '8px'
        }}>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            <LockOutlined style={{ marginRight: '4px' }} />
            此页面受密码保护，请联系管理员获取访问密码
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default PasswordAuth;
