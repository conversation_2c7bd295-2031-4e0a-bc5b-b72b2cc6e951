import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Space,
  Select,
  message,
  Tooltip,
  InputNumber,
  Spin,
  Divider
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import {
  AliVocabularyService,
  Hotword,
  VocabularyDetail,
  VocabularyListItem
} from '../services/aliVocabularyService';

const { Option } = Select;

interface HotwordManagerProps {
  apiKey: string;
}

const HotwordManager: React.FC<HotwordManagerProps> = ({ apiKey }) => {
  const [vocabularies, setVocabularies] = useState<VocabularyListItem[]>([]);
  const [totalCount, setTotalCount] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [createModalVisible, setCreateModalVisible] = useState<boolean>(false);
  const [viewModalVisible, setViewModalVisible] = useState<boolean>(false);
  const [editModalVisible, setEditModalVisible] = useState<boolean>(false);
  const [selectedVocabularyId, setSelectedVocabularyId] = useState<string>('');
  const [currentVocabulary, setCurrentVocabulary] = useState<VocabularyDetail | null>(null);
  const [searchPrefix, setSearchPrefix] = useState<string>('');
  const [createForm] = Form.useForm();
  const [editForm] = Form.useForm();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);

  const vocabularyService = AliVocabularyService.getInstance();

  // 加载热词表列表
  const loadVocabularies = async (prefix?: string) => {
    if (!apiKey) {
      message.error('请先设置API密钥');
      return;
    }

    setLoading(true);
    try {
      const response = await vocabularyService.listVocabularies({
        apiKey,
        prefix,
        pageIndex: currentPage - 1,
        pageSize
      });

      if (response.output && response.output.vocabularies) {
        setVocabularies(response.output.vocabularies);
        setTotalCount(response.output.total || 0);
      } else {
        setVocabularies([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error('获取热词表列表失败:', error);
      message.error('获取热词表列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 首次加载
  useEffect(() => {
    if (apiKey) {
      loadVocabularies();
    }
  }, [apiKey, currentPage, pageSize]);

  // 轮询任务状态
  const pollTaskStatus = async (apiKey: string, taskId: string): Promise<string> => {
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          const taskStatus = await vocabularyService.checkTaskStatus(apiKey, taskId);

          if (taskStatus.status === 'SUCCEEDED') {
            // 任务成功，返回热词表ID
            if (taskStatus.output && taskStatus.output.vocabulary_id) {
              resolve(taskStatus.output.vocabulary_id);
            } else {
              reject(new Error('任务成功但未获取到热词表ID'));
            }
          } else if (taskStatus.status === 'FAILED') {
            reject(new Error('任务执行失败'));
          } else {
            // 任务还在进行中，继续轮询
            setTimeout(poll, 2000);
          }
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  };

  // 创建热词表
  const handleCreateVocabulary = async (values: any) => {
    if (!apiKey) {
      message.error('请先设置API密钥');
      return;
    }

    setLoading(true);
    try {
      const formattedHotwords: Hotword[] = values.hotwords.map((item: any) => ({
        name: item.text,
        weight: Number(item.weight),
        lang: item.lang
      }));

      // 创建热词表并获取任务ID
      const response = await vocabularyService.createVocabulary({
        apiKey,
        prefix: values.prefix,
        vocabulary: formattedHotwords,
        targetModel: values.targetModel
      });

      if (response.task_id) {
        message.success('热词表创建任务已提交，正在等待处理结果...');

        // 轮询任务状态
        try {
          const vocabularyId = await pollTaskStatus(apiKey, response.task_id);
          message.success(`热词表创建成功，ID: ${vocabularyId}`);
          loadVocabularies();
        } catch (error: any) {
          message.error(`热词表创建失败: ${error.message}`);
        }
      } else {
        message.error('创建热词表失败: 未获取到任务ID');
      }

      setCreateModalVisible(false);
      createForm.resetFields();
    } catch (error) {
      console.error('创建热词表失败:', error);
      message.error('创建热词表失败');
    } finally {
      setLoading(false);
    }
  };

  // 查看热词表详情
  const handleViewVocabulary = async (vocabularyId: string) => {
    if (!apiKey) {
      message.error('请先设置API密钥');
      return;
    }

    setLoading(true);
    try {
      const response = await vocabularyService.queryVocabulary({
        apiKey,
        vocabularyId
      });

      if (response.output) {
        const detail: VocabularyDetail = {
          vocabulary_id: vocabularyId,
          prefix: response.output.prefix || '',
          target_model: response.output.target_model || '',
          created_at: response.output.created_at || '',
          updated_at: response.output.updated_at || '',
          vocabulary: response.output.vocabulary || []
        };
        setCurrentVocabulary(detail);
        setViewModalVisible(true);
      }
    } catch (error) {
      console.error('获取热词表详情失败:', error);
      message.error('获取热词表详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 编辑热词表
  const handleEditVocabulary = async (vocabularyId: string) => {
    if (!apiKey) {
      message.error('请先设置API密钥');
      return;
    }

    setLoading(true);
    try {
      const response = await vocabularyService.queryVocabulary({
        apiKey,
        vocabularyId
      });

      if (response.output && response.output.vocabulary) {
        setSelectedVocabularyId(vocabularyId);
        editForm.setFieldsValue({
          hotwords: response.output.vocabulary.map(item => ({
            text: item.name,
            weight: item.weight,
            lang: item.lang || 'zh'
          }))
        });
        setEditModalVisible(true);
      }
    } catch (error) {
      console.error('获取热词表详情失败:', error);
      message.error('获取热词表详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新热词表
  const handleUpdateVocabulary = async (values: any) => {
    if (!apiKey || !selectedVocabularyId) {
      message.error('请先设置API密钥或选择热词表');
      return;
    }

    setLoading(true);
    try {
      const formattedHotwords: Hotword[] = values.hotwords.map((item: any) => ({
        name: item.text,
        weight: Number(item.weight),
        lang: item.lang
      }));

      const response = await vocabularyService.updateVocabulary({
        apiKey,
        vocabularyId: selectedVocabularyId,
        vocabulary: formattedHotwords
      });

      if (response.task_id) {
        message.success('热词表更新任务已提交，正在等待处理结果...');

        // 轮询任务状态
        try {
          await pollTaskStatus(apiKey, response.task_id);
          message.success('热词表更新成功');
          loadVocabularies();
        } catch (error: any) {
          message.error(`热词表更新失败: ${error.message}`);
        }
      } else {
        message.error('更新热词表失败: 未获取到任务ID');
      }

      setEditModalVisible(false);
      editForm.resetFields();
    } catch (error) {
      console.error('更新热词表失败:', error);
      message.error('更新热词表失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除热词表
  const handleDeleteVocabulary = async (vocabularyId: string) => {
    if (!apiKey) {
      message.error('请先设置API密钥');
      return;
    }

    setLoading(true);
    try {
      await vocabularyService.deleteVocabulary({
        apiKey,
        vocabularyId
      });

      message.success('热词表删除成功');
      loadVocabularies();
    } catch (error) {
      console.error('删除热词表失败:', error);
      message.error('删除热词表失败');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: '热词表ID',
      dataIndex: 'vocabulary_id',
      key: 'vocabulary_id',
      ellipsis: true,
      width: 200,
    },
    {
      title: '前缀',
      dataIndex: 'prefix',
      key: 'prefix',
      width: 120,
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text: string) => text ? new Date(text).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_: any, record: VocabularyListItem) => (
        <Space size="middle">
          <Button
            type="link"
            size="small"
            onClick={() => handleViewVocabulary(record.vocabulary_id)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => handleEditVocabulary(record.vocabulary_id)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            onClick={() => {
              Modal.confirm({
                title: '确认删除',
                content: '确定要删除这个热词表吗？此操作不可恢复。',
                onOk: () => handleDeleteVocabulary(record.vocabulary_id),
              });
            }}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <Card title="热词表管理" variant="outlined">
      <Spin spinning={loading}>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Input
              placeholder="输入前缀搜索热词表"
              value={searchPrefix}
              onChange={e => setSearchPrefix(e.target.value)}
              style={{ width: 200 }}
            />
            <Button
              type="primary"
              onClick={() => loadVocabularies(searchPrefix || undefined)}
              icon={<SearchOutlined />}
            >
              搜索
            </Button>
            <Button
              onClick={() => {
                setSearchPrefix('');
                loadVocabularies();
              }}
              icon={<ReloadOutlined />}
            >
              重置
            </Button>
          </Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            创建热词表
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={vocabularies}
          rowKey="vocabulary_id"
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: totalCount,
            onChange: (page, size) => {
              setCurrentPage(page);
              setPageSize(size || 10);
            },
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />

        {/* 创建热词表弹窗 */}
        <Modal
          title="创建热词表"
          open={createModalVisible}
          onCancel={() => setCreateModalVisible(false)}
          footer={null}
          width={800}
        >
          <Form
            form={createForm}
            onFinish={handleCreateVocabulary}
            layout="vertical"
            initialValues={{
              targetModel: 'paraformer-realtime-v2',
              hotwords: [{ text: '', weight: 4, lang: 'zh' }]
            }}
          >
            <Form.Item
              name="prefix"
              label="热词表前缀"
              rules={[
                { required: true, message: '请输入热词表前缀' },
                { max: 10, message: '前缀不能超过10个字符' },
                { pattern: /^[a-z0-9]+$/, message: '前缀只能包含小写字母和数字' }
              ]}
              tooltip="仅允许数字和小写字母，不超过10个字符"
            >
              <Input placeholder="请输入热词表前缀" />
            </Form.Item>

            <Form.Item
              name="targetModel"
              label="目标模型"
              tooltip="选择要应用热词表的语音识别模型"
            >
              <Select>
                <Option value="paraformer-realtime-v2">paraformer-realtime-v2</Option>
              </Select>
            </Form.Item>

            <Form.List name="hotwords">
              {(fields, { add, remove }) => (
                <>
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                    热词列表
                    <Tooltip title="添加需要提高识别准确率的词汇，权重越高优先级越高">
                      <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                    </Tooltip>
                  </div>
                  {fields.map(({ key, name, ...restField }) => (
                    <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                      <Form.Item
                        {...restField}
                        name={[name, 'text']}
                        rules={[{ required: true, message: '请输入热词' }]}
                        style={{ flex: 1, minWidth: 200 }}
                      >
                        <Input placeholder="热词" />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, 'weight']}
                        rules={[{ required: true, message: '请输入权重' }]}
                      >
                        <InputNumber min={1} max={10} placeholder="权重" style={{ width: 80 }} />
                      </Form.Item>
                      <Form.Item
                        {...restField}
                        name={[name, 'lang']}
                        rules={[{ required: true, message: '请选择语言' }]}
                      >
                        <Select style={{ width: 100 }}>
                          <Option value="zh">中文</Option>
                          <Option value="en">英文</Option>
                        </Select>
                      </Form.Item>
                      <Button danger onClick={() => remove(name)} icon={<DeleteOutlined />} />
                    </Space>
                  ))}
                  <Form.Item>
                    <Button type="dashed" onClick={() => add({ text: '', weight: 4, lang: 'zh' })} block icon={<PlusOutlined />}>
                      添加热词
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  创建
                </Button>
                <Button onClick={() => setCreateModalVisible(false)}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        {/* 查看热词表详情弹窗 */}
        <Modal
          title="热词表详情"
          open={viewModalVisible}
          onCancel={() => setViewModalVisible(false)}
          footer={[
            <Button key="close" onClick={() => setViewModalVisible(false)}>
              关闭
            </Button>
          ]}
          width={800}
        >
          {currentVocabulary && (
            <div>
              <Divider orientation="left">基本信息</Divider>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>热词表ID:{currentVocabulary.vocabulary_id}</div>
                <div>前缀:{currentVocabulary.prefix}</div>
                <div>目标模型:{currentVocabulary.target_model}</div>
                <div>创建时间:{new Date(currentVocabulary.created_at).toLocaleString()}</div>
                <div>更新时间:{new Date(currentVocabulary.updated_at).toLocaleString()}</div>
              </Space>

              <Divider orientation="left">热词列表</Divider>
              <Table
                dataSource={currentVocabulary.vocabulary}
                columns={[
                  { title: '热词', dataIndex: 'name', key: 'name' },
                  { title: '权重', dataIndex: 'weight', key: 'weight' },
                  { title: '语言', dataIndex: 'lang', key: 'lang', render: (lang: string) => lang === 'zh' ? '中文' : '英文' }
                ]}
                pagination={false}
                size="small"
                rowKey="name"
              />
            </div>
          )}
        </Modal>

        {/* 编辑热词表弹窗 */}
        <Modal
          title="编辑热词表"
          open={editModalVisible}
          onCancel={() => setEditModalVisible(false)}
          footer={null}
          width={800}
        >
          {editModalVisible && (
            <Form
              form={editForm}
              onFinish={handleUpdateVocabulary}
              layout="vertical"
            >
              <Form.List name="hotwords">
                {(fields, { add, remove }) => (
                  <>
                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                      热词列表
                      <Tooltip title="编辑热词内容，权重越高优先级越高">
                        <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                      </Tooltip>
                    </div>
                    {fields.map(({ key, name, ...restField }) => (
                      <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                        <Form.Item
                          {...restField}
                          name={[name, 'text']}
                          rules={[{ required: true, message: '请输入热词' }]}
                          style={{ flex: 1, minWidth: 200 }}
                        >
                          <Input placeholder="热词" />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, 'weight']}
                          rules={[{ required: true, message: '请输入权重' }]}
                        >
                          <InputNumber min={1} max={10} placeholder="权重" style={{ width: 80 }} />
                        </Form.Item>
                        <Form.Item
                          {...restField}
                          name={[name, 'lang']}
                          rules={[{ required: true, message: '请选择语言' }]}
                        >
                          <Select style={{ width: 100 }}>
                            <Option value="zh">中文</Option>
                            <Option value="en">英文</Option>
                          </Select>
                        </Form.Item>
                        <Button danger onClick={() => remove(name)} icon={<DeleteOutlined />} />
                      </Space>
                    ))}
                    <Form.Item>
                      <Button type="dashed" onClick={() => add({ text: '', weight: 4, lang: 'zh' })} block icon={<PlusOutlined />}>
                        添加热词
                      </Button>
                    </Form.Item>
                  </>
                )}
              </Form.List>

              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit">
                    更新
                  </Button>
                  <Button onClick={() => setEditModalVisible(false)}>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          )}
        </Modal>
      </Spin>
    </Card>
  );
};

export default HotwordManager;
