import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { ConfigService } from '../services/configService';
import { DigitalHumanConfig } from '../types/config';
import figuresData from '../config/figures.json';
import voicesData from '../config/voices.json';
import Draggable from 'react-draggable';
import { ResizableBox } from 'react-resizable';
import 'react-resizable/css/styles.css';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  Layout,
  Button,
  Select,
  Slider,
  Switch,
  Modal,
  Card,
  Row,
  Col,
  Space,
  Typography,
  message,
  Input,
  Tooltip,
  Upload,
  List,
  Tabs,
  Checkbox,
  InputNumber
} from 'antd';
import {
  SaveOutlined,
  UndoOutlined,
  RollbackOutlined,
  SettingOutlined,
  UserOutlined,
  SoundOutlined,
  VideoCameraOutlined,
  CopyOutlined,
  InfoCircleOutlined,
  MinusCircleOutlined,
  PlusOutlined,
  InboxOutlined,
  MenuOutlined
} from '@ant-design/icons';
import '../App.css';
import { UploadService } from '../services/uploadService';
import KnowledgeBaseManager from './KnowledgeBaseManager';

const { Header, Content } = Layout;
const { Title, Text } = Typography;
const { Option } = Select;

const DEFAULT_CONFIG: DigitalHumanConfig = {
  ttsPitch: 5,
  ttsSpeed: 5,
  ttsVolume: 5,
  ttsPer: 'CAP_4193',
  cameraId: '2',
  figureId: '234340',
  autoChromaKey: true,
  showConfigButton: true,
  outputAspectRatio: '9:16',
  isTransparent: false,
  backgroundImageUrl: '',
  defaultBackgroundUrl: '',
  debugToolsEnabled: true,
  customParams: [
    { key: 'cp-preAlertSec', value: '120' }
  ],
  openaiApiKey: '',
  openaiApiUrl: 'https://oneapi.jingyuncenter.com/v1/chat/completions',
  openaiModel: 'doubao-1-5-pro-32k-250115',
  max_tokens: 100, // 添加默认值
  systemPrompt: '你是井云数字人助手，能够友好地回答用户的问题。请简明扼要地回答，并保持礼貌和专业。',
  // 添加知识库默认配置
  knowledgeBaseApiKey: '',
  knowledgeBaseApiUrl: '',
  knowledgeBaseEnabled: false,
  // 其他配置
  keywordBackgrounds: [],
  autoResetBackground: true,
  voiceWakeupEnabled: false,
  voiceWakeupPhrases: ['小金小金'],
  voiceWakeupTimeout: 300,
  allowInterruption: false,
  interruptionKeywords: ['小金小金'],
  showInputField: true,
  carouselEnabled: false,
  carouselInterval: 5,
  carouselItems: [],
  carouselPauseOnKeyword: true,
  carouselWaitForVideoEnd: false,
  videoSoundEnabled: false,
  videoPlayPhrases: ['播放视频', '打开视频声音'],
  keywordDisplayDuration: 5, // 默认5秒后自动切换回默认背景
  // 阿里云语音识别配置
  aliASREnabled: false,
  aliASRApiKey: '',
  aliASRSampleRate: 16000,
  aliASRFormat: 'wav',
  aliASREnablePunctuation: true,
  aliASREnableInterimResult: true,
  aliASRResultTimeoutMs: 1500, // 默认1.5秒没有新结果就将当前结果标记为最终结果
  // 访问密码配置
  accessPasswordEnabled: false,
  accessPassword: ''
};

// 从配置文件导入形象和音色数据
const FIGURES = figuresData.figureList;
const VOICES = voicesData.voiceList;

interface UploadedBackground {
  id: string;
  url: string;
  type: 'image' | 'video';
  name: string;
}

// 可拖拽的轮播项目组件
interface SortableCarouselItemProps {
  id: string;
  item: any;
  background: UploadedBackground | undefined;
  onToggle: (id: string) => void;
  onRemove: (id: string) => void;
}

const SortableCarouselItem: React.FC<SortableCarouselItemProps> = ({
  id,
  item,
  background,
  onToggle,
  onRemove
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div ref={setNodeRef} style={style}>
      <Card
        size="small"
        style={{
          marginBottom: '8px',
          opacity: item.enabled ? 1 : 0.6,
          border: item.enabled ? '1px solid #1890ff' : '1px solid #d9d9d9'
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <div
            {...attributes}
            {...listeners}
            style={{ cursor: 'grab', color: '#666' }}
          >
            <MenuOutlined />
          </div>
          <div style={{ flex: 1, display: 'flex', alignItems: 'center', gap: '8px' }}>
            {background && (
              <div style={{ width: '40px', height: '40px', overflow: 'hidden' }}>
                {background.type === 'image' ? (
                  <img
                    src={background.url}
                    alt={background.name}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      borderRadius: '4px'
                    }}
                  />
                ) : (
                  <video
                    src={background.url}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover',
                      borderRadius: '4px'
                    }}
                    muted
                  />
                )}
              </div>
            )}
            <div style={{ flex: 1 }}>
              <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                {background?.name || '未知素材'}
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                {background?.type === 'image' ? '图片' : '视频'}
              </div>
            </div>
          </div>
          <Checkbox
            checked={item.enabled}
            onChange={() => onToggle(id)}
          />
          <Button
            type="text"
            danger
            size="small"
            icon={<MinusCircleOutlined />}
            onClick={() => onRemove(id)}
          />
        </div>
      </Card>
    </div>
  );
};

const ConfigPage: React.FC = () => {
  const navigate = useNavigate();
  const [config, setConfig] = useState<DigitalHumanConfig>({
    ...DEFAULT_CONFIG,
    customParams: DEFAULT_CONFIG.customParams || []
  });
  const [error, setError] = useState<string | null>(null);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle');
  const [showFigureModal, setShowFigureModal] = useState(false);
  const [showVoiceModal, setShowVoiceModal] = useState(false);
  const [selectedGender, setSelectedGender] = useState('ALL_GENDER');
  const [selectedStyle, setSelectedStyle] = useState('ALL_STYLE');
  const [selectedPose, setSelectedPose] = useState('ALL_POSE');
  const [selectedBackground, setSelectedBackground] = useState('ALL_BACKGROUND');
  const [selectedAction, setSelectedAction] = useState('ALL_ACTION');
  const [selectedVoiceGender, setSelectedVoiceGender] = useState('ALL_GENDER');
  const [selectedVoiceAge, setSelectedVoiceAge] = useState('ALL_AGE');
  const [selectedVoiceStyle, setSelectedVoiceStyle] = useState('ALL_STYLE');
  const [isUploadModalVisible, setIsUploadModalVisible] = useState(false);
  const [uploadedBackgrounds, setUploadedBackgrounds] = useState<UploadedBackground[]>([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewUrl, setPreviewUrl] = useState('');
  const [customFileName, setCustomFileName] = useState('');
  const [activeTab, setActiveTab] = useState('basic');
  const [previewSize, setPreviewSize] = useState({ width: 402, height: 712 });
  const [previewPosition, setPreviewPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [containerSize, setContainerSize] = useState({ width: 800, height: 600 });
  const containerRef = useRef<HTMLDivElement>(null);
  const draggableRef = useRef<HTMLDivElement>(null);

  // 拖拽传感器配置
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    loadConfig();
    loadUploadedBackgrounds();
  }, []);

  useEffect(() => {
    const size = config.previewSize || calculatePreviewSize();
    setPreviewSize(size);

    // 从配置中读取位置，如果没有则使用默认值（居中）
    const position = config.previewPosition || { x: 50, y: 50 };
    setPreviewPosition(position);
  }, [config.outputAspectRatio, config.previewSize, config.previewPosition]);

  const loadConfig = async () => {
    try {
      const configService = ConfigService.getInstance();
      const savedConfig = await configService.getConfig();

      // 确保所有配置项都存在，包括新增的轮播配置项
      const configWithDefaults = {
        ...DEFAULT_CONFIG, // 先使用默认配置作为基础
        ...savedConfig, // 然后覆盖已保存的配置
        voiceWakeupPhrases: savedConfig.voiceWakeupPhrases || ['小金小金'],
        interruptionKeywords: savedConfig.interruptionKeywords || ['小金小金'],
        customParams: savedConfig.customParams || [],
        carouselEnabled: savedConfig.carouselEnabled !== undefined ? savedConfig.carouselEnabled : DEFAULT_CONFIG.carouselEnabled,
        carouselInterval: savedConfig.carouselInterval || DEFAULT_CONFIG.carouselInterval,
        carouselItems: savedConfig.carouselItems || DEFAULT_CONFIG.carouselItems,
        carouselPauseOnKeyword: savedConfig.carouselPauseOnKeyword !== undefined ? savedConfig.carouselPauseOnKeyword : DEFAULT_CONFIG.carouselPauseOnKeyword,
        carouselWaitForVideoEnd: savedConfig.carouselWaitForVideoEnd !== undefined ? savedConfig.carouselWaitForVideoEnd : DEFAULT_CONFIG.carouselWaitForVideoEnd,
        // 阿里云语音识别配置
        aliASREnabled: savedConfig.aliASREnabled !== undefined ? savedConfig.aliASREnabled : DEFAULT_CONFIG.aliASREnabled,
        aliASRApiKey: savedConfig.aliASRApiKey || DEFAULT_CONFIG.aliASRApiKey,
        aliASRSampleRate: savedConfig.aliASRSampleRate !== undefined ? savedConfig.aliASRSampleRate : DEFAULT_CONFIG.aliASRSampleRate,
        aliASRFormat: savedConfig.aliASRFormat || DEFAULT_CONFIG.aliASRFormat,
        aliASREnablePunctuation: savedConfig.aliASREnablePunctuation !== undefined ? savedConfig.aliASREnablePunctuation : DEFAULT_CONFIG.aliASREnablePunctuation,
        aliASREnableInterimResult: savedConfig.aliASREnableInterimResult !== undefined ? savedConfig.aliASREnableInterimResult : DEFAULT_CONFIG.aliASREnableInterimResult,
        aliASRResultTimeoutMs: savedConfig.aliASRResultTimeoutMs !== undefined ? savedConfig.aliASRResultTimeoutMs : DEFAULT_CONFIG.aliASRResultTimeoutMs,
        // 访问密码配置
        accessPasswordEnabled: savedConfig.accessPasswordEnabled !== undefined ? savedConfig.accessPasswordEnabled : DEFAULT_CONFIG.accessPasswordEnabled,
        accessPassword: savedConfig.accessPassword || DEFAULT_CONFIG.accessPassword
      };

      setConfig(configWithDefaults);
      setError(null);
    } catch (err) {
      setError('加载配置失败，使用默认配置');
      console.error('加载配置失败:', err);
      setConfig({
        ...DEFAULT_CONFIG,
        customParams: DEFAULT_CONFIG.customParams || []
      });
    }
  };

  const loadUploadedBackgrounds = async () => {
    try {
      const uploadService = UploadService.getInstance();
      const backgrounds = await uploadService.getUploadedBackgrounds();
      setUploadedBackgrounds(backgrounds);
    } catch (error) {
      console.error('加载背景列表失败:', error);
      message.error('加载背景列表失败');
    }
  };

  // 保存配置到服务器
  const saveConfig = async () => {
    try {
      setSaveStatus('saving');
      const configService = ConfigService.getInstance();
      await configService.saveConfig(config);
      setSaveStatus('success');
      setTimeout(() => setSaveStatus('idle'), 2000);
      setError(null);
    } catch (err) {
      setSaveStatus('error');
      setError('保存配置失败');
      console.error('保存配置失败:', err);
      setTimeout(() => setSaveStatus('idle'), 2000);
    }
  };

  // 重置配置
  const resetConfig = () => {
    setConfig(DEFAULT_CONFIG);
  };

  // 返回主页
  const goBack = () => {
    navigate('/');
  };

  const handleFigureSelect = (figureId: string) => {
    setConfig({ ...config, figureId });
    setShowFigureModal(false);
  };

  const handleVoiceSelect = (voiceId: string) => {
    const selectedVoice = VOICES.find(v => v.per === voiceId);
    if (selectedVoice) {
      setConfig({ ...config, ttsPer: selectedVoice.per });
      setShowVoiceModal(false);
    }
  };


  // 计算预览容器的尺寸
  const calculatePreviewSize = () => {
    const containerWidth = 800; // 预览容器的宽度
    const containerHeight = 600; // 预览容器的高度
    const aspectRatio = config.outputAspectRatio === '9:16' ? 9 / 16 : 16 / 9;

    let width, height;
    if (config.outputAspectRatio === '9:16') {
      height = containerHeight;
      width = height * aspectRatio;
    } else {
      width = containerWidth;
      height = width / aspectRatio;
    }

    return { width, height };
  };

  // 更新容器尺寸
  const updateContainerSize = useCallback(() => {
    if (containerRef.current) {
      const { clientWidth, clientHeight } = containerRef.current;
      setContainerSize({
        width: clientWidth,
        height: clientHeight
      });
    }
  }, []);

  // 监听容器尺寸变化
  useEffect(() => {
    if (!containerRef.current) return;

    const resizeObserver = new ResizeObserver(() => {
      updateContainerSize();
    });

    resizeObserver.observe(containerRef.current);
    return () => resizeObserver.disconnect();
  }, [updateContainerSize]);

  // 初始化容器尺寸
  useEffect(() => {
    updateContainerSize();
  }, [updateContainerSize]);

  // 计算实际位置
  const calculateActualPosition = (position: { x: number; y: number }) => {
    // 使用实际容器尺寸
    const { width, height } = containerSize;
    // 将百分比位置转换为以中心点为原点的坐标
    return {
      x: (position.x / 100) * width - width / 2,
      y: (position.y / 100) * height - height / 2
    };
  };

  // 轮播项目处理函数
  const handleCarouselDragEnd = (event: any) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      const oldIndex = config.carouselItems.findIndex(item => item.id === active.id);
      const newIndex = config.carouselItems.findIndex(item => item.id === over.id);

      const newCarouselItems = arrayMove(config.carouselItems, oldIndex, newIndex);

      // 重新分配order值
      const updatedItems = newCarouselItems.map((item, index) => ({
        ...item,
        order: index
      }));

      setConfig({ ...config, carouselItems: updatedItems });
    }
  };

  const addCarouselItem = (backgroundId: string) => {
    const newId = Date.now().toString();
    const newItem = {
      id: newId,
      backgroundId,
      order: config.carouselItems.length,
      enabled: true
    };

    setConfig({
      ...config,
      carouselItems: [...config.carouselItems, newItem]
    });
  };

  const removeCarouselItem = (itemId: string) => {
    const newItems = config.carouselItems.filter(item => item.id !== itemId);
    // 重新分配order值
    const updatedItems = newItems.map((item, index) => ({
      ...item,
      order: index
    }));

    setConfig({ ...config, carouselItems: updatedItems });
  };

  const toggleCarouselItem = (itemId: string) => {
    const newItems = config.carouselItems.map(item =>
      item.id === itemId ? { ...item, enabled: !item.enabled } : item
    );

    setConfig({ ...config, carouselItems: newItems });
  };

  const selectedFigure = FIGURES.find(f => f.id.toString() === config.figureId) || FIGURES[0];
  const selectedVoice = VOICES.find(v => v.per === config.ttsPer) || VOICES[0];

  return (
    <Layout className="config-page" style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #e6eeff 0%, #d6e9ff 50%, #e6eeff 100%)'
    }}>
      <Header style={{
        background: 'rgba(255, 255, 255, 0.95)',
        padding: '0 24px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.12)',
        position: 'relative',
        zIndex: 1,
        borderRadius: '12px',
        margin: '0 16px',
        backdropFilter: 'blur(8px)'
      }}>
        <Title level={3} style={{ margin: 0 }}>井云交互数字人配置</Title>
        <Space>
          <Button
            onClick={resetConfig}
            icon={<UndoOutlined />}
          >
            重置配置
          </Button>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={saveConfig}
            loading={saveStatus === 'saving'}
          >
            {saveStatus === 'saving' ? '保存中...' :
              saveStatus === 'success' ? '保存成功' :
                saveStatus === 'error' ? '保存失败' : '保存配置'}
          </Button>
          <Button
            onClick={goBack}
            icon={<RollbackOutlined />}
          >
            返回主页
          </Button>
        </Space>
      </Header>

      {error && (
        <div style={{
          padding: '16px',
          background: 'rgba(255, 242, 240, 0.9)',
          border: '1px solid #ffccc7',
          margin: '16px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(255, 77, 79, 0.1)',
          backdropFilter: 'blur(8px)'
        }}>
          <Text type="danger">{error}</Text>
        </div>
      )}

      <Content style={{
        padding: '24px',
        background: 'transparent'
      }}>
        <Row gutter={24}>
          <Col span={8}>
            <div style={{
              height: 'calc(100vh - 180px)',
              overflowY: 'auto',
              paddingRight: '12px',
              scrollbarWidth: 'none',
              msOverflowStyle: 'none',
            }} className="hide-scrollbar">
              <Tabs
                type="card"
                activeKey={activeTab}
                onChange={setActiveTab}
                items={[
                  {
                    key: 'basic',
                    label: '数字人配置',
                    children: (
                      <>
                        <Card
                          title={<Space><UserOutlined /> 形象</Space>}
                          style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Card
                            hoverable
                            onClick={() => setShowFigureModal(true)}
                            styles={{ body: { padding: '12px' } }}
                          >
                            <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                              <div style={{
                                width: '80px',
                                height: '80px',
                                overflow: 'hidden',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexShrink: 0
                              }}>
                                <img
                                  alt={selectedFigure.name}
                                  src={selectedFigure.templateImg}
                                  style={{
                                    maxWidth: '100%',
                                    maxHeight: '100%',
                                    objectFit: 'contain'
                                  }}
                                />
                              </div>
                              <div style={{ flex: 1 }}>
                                <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '4px' }}>
                                  {selectedFigure.name}
                                </div>
                                <div style={{ color: '#666' }}>
                                  ID: {selectedFigure.id}
                                </div>
                                <div style={{ marginTop: '8px', display: 'flex', gap: '8px', alignItems: 'center' }}>
                                  <Input
                                    placeholder="直接输入形象ID"
                                    onClick={(e) => e.stopPropagation()}
                                    onPressEnter={(e) => {
                                      e.stopPropagation();
                                      const inputValue = (e.target as HTMLInputElement).value;
                                      if (inputValue.trim()) {
                                        setConfig({ ...config, figureId: inputValue.trim() });
                                      }
                                    }}
                                    style={{ flex: 1 }}
                                  />
                                  <Button
                                    type="primary"
                                    size="small"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                                      if (input && input.value.trim()) {
                                        setConfig({ ...config, figureId: input.value.trim() });
                                        message.success('已设置形象ID');
                                      }
                                    }}
                                  >
                                    应用
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </Card>
                        </Card>

                        <Card
                          title={<Space><SoundOutlined /> 声音</Space>}
                          style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Card
                            hoverable
                            onClick={() => setShowVoiceModal(true)}
                            styles={{ body: { padding: '12px' } }}
                          >
                            <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                              <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                                <div
                                  style={{
                                    width: '60px',
                                    height: '60px',
                                    overflow: 'hidden',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    flexShrink: 0,
                                    position: 'relative'
                                  }}
                                >
                                  <img
                                    alt={selectedVoice.name}
                                    src={selectedVoice.thumbnailUrl}
                                    style={{
                                      maxWidth: '100%',
                                      maxHeight: '100%',
                                      objectFit: 'contain'
                                    }}
                                  />
                                </div>
                                <div style={{ flex: 1 }}>
                                  <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '4px' }}>
                                    {selectedVoice.name}
                                  </div>
                                  <div style={{ color: '#666', fontSize: '12px', display: 'flex', alignItems: 'center', gap: '4px' }}>
                                    ID: {selectedVoice.per}
                                    <Button
                                      type="text"
                                      size="small"
                                      icon={<CopyOutlined />}
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        navigator.clipboard.writeText(selectedVoice.per);
                                        message.success('ID已复制到剪贴板');
                                      }}
                                      style={{ padding: '0 4px' }}
                                    />
                                  </div>
                                  <div style={{ marginTop: '8px', display: 'flex', gap: '8px', alignItems: 'center' }}>
                                    <Input
                                      placeholder="直接输入声音ID"
                                      onClick={(e) => e.stopPropagation()}
                                      onPressEnter={(e) => {
                                        e.stopPropagation();
                                        const inputValue = (e.target as HTMLInputElement).value;
                                        if (inputValue.trim()) {
                                          setConfig({ ...config, ttsPer: inputValue.trim() });
                                        }
                                      }}
                                      style={{ flex: 1 }}
                                    />
                                    <Button
                                      type="primary"
                                      size="small"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        const input = e.currentTarget.previousElementSibling as HTMLInputElement;
                                        if (input && input.value.trim()) {
                                          setConfig({ ...config, ttsPer: input.value.trim() });
                                          message.success('已设置声音ID');
                                        }
                                      }}
                                    >
                                      应用
                                    </Button>
                                  </div>
                                </div>
                              </div>
                              <div style={{ fontSize: '12px', color: '#666' }}>
                                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                                  {selectedVoice.tagListDetail.map((tag, index) => (
                                    <span
                                      key={index}
                                      style={{
                                        background: '#f0f0f0',
                                        padding: '2px 6px',
                                        borderRadius: '4px',
                                        fontSize: '12px'
                                      }}
                                    >
                                      {tag}
                                    </span>
                                  ))}
                                </div>
                              </div>
                            </div>
                          </Card>
                        </Card>

                        <Card
                          title={<Space><SettingOutlined /> 声音设置</Space>}
                          style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div>
                              <Text>音调 (1-10):</Text>
                              <Slider
                                min={1}
                                max={10}
                                value={config.ttsPitch}
                                onChange={(value) => setConfig({ ...config, ttsPitch: value })}
                              />
                            </div>
                            <div>
                              <Text>语速 (1-10):</Text>
                              <Slider
                                min={1}
                                max={10}
                                value={config.ttsSpeed}
                                onChange={(value) => setConfig({ ...config, ttsSpeed: value })}
                              />
                            </div>
                            <div>
                              <Text>音量 (1-10):</Text>
                              <Slider
                                min={1}
                                max={10}
                                value={config.ttsVolume}
                                onChange={(value) => setConfig({ ...config, ttsVolume: value })}
                              />
                            </div>
                          </Space>
                        </Card>

                        <Card
                          title={<Space><SettingOutlined /> 高级设置</Space>}
                          style={{
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div>
                              <Switch
                                checked={config.autoChromaKey}
                                onChange={(checked) => setConfig({ ...config, autoChromaKey: checked })}
                              />
                              <Text style={{ marginLeft: '8px' }}>自动抠图</Text>
                            </div>
                          </Space>
                        </Card>

                        <Card
                          title={<Space><SettingOutlined /> 基础输出设置</Space>}
                          style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div>
                              <Text>输出画面</Text>
                              <div style={{ display: 'flex', gap: '16px', marginTop: '8px' }}>
                                <div
                                  className={`resolution-item ${config.outputAspectRatio === '16:9' ? 'selected' : ''}`}
                                  onClick={() => setConfig({ ...config, outputAspectRatio: '16:9' })}
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    padding: '8px 16px',
                                    border: '1px solid #d9d9d9',
                                    borderRadius: '4px',
                                    cursor: 'pointer',
                                    background: config.outputAspectRatio === '16:9' ? '#e6f7ff' : 'transparent',
                                    borderColor: config.outputAspectRatio === '16:9' ? '#1890ff' : '#d9d9d9'
                                  }}
                                >
                                  <span style={{
                                    width: '24px',
                                    height: '16px',
                                    background: '#d9d9d9',
                                    borderRadius: '2px'
                                  }}></span>
                                  <div>
                                    <div style={{ fontWeight: 'bold' }}>16:9</div>
                                    <div style={{ fontSize: '12px', color: '#666' }}>1920*1080</div>
                                  </div>
                                </div>
                                <div
                                  className={`resolution-item ${config.outputAspectRatio === '9:16' ? 'selected' : ''}`}
                                  onClick={() => setConfig({ ...config, outputAspectRatio: '9:16' })}
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px',
                                    padding: '8px 16px',
                                    border: '1px solid #d9d9d9',
                                    borderRadius: '4px',
                                    cursor: 'pointer',
                                    background: config.outputAspectRatio === '9:16' ? '#e6f7ff' : 'transparent',
                                    borderColor: config.outputAspectRatio === '9:16' ? '#1890ff' : '#d9d9d9'
                                  }}
                                >
                                  <span style={{
                                    width: '16px',
                                    height: '24px',
                                    background: '#d9d9d9',
                                    borderRadius: '2px'
                                  }}></span>
                                  <div>
                                    <div style={{ fontWeight: 'bold' }}>9:16</div>
                                    <div style={{ fontSize: '12px', color: '#666' }}>1080*1920</div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </Space>
                        </Card>

                        <Card
                          title={<Space><SettingOutlined /> 高级输出设置</Space>}
                          style={{
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                <Text>是否透明</Text>
                                <Tooltip title="设置数字人背景是否透明">
                                  <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                </Tooltip>
                              </div>
                              <div style={{ display: 'flex', gap: '8px' }}>
                                <div
                                  className={`button-item ${!config.isTransparent ? 'selected' : ''}`}
                                  onClick={() => setConfig({ ...config, isTransparent: false })}
                                  style={{
                                    padding: '8px 16px',
                                    border: '1px solid #d9d9d9',
                                    borderRadius: '4px',
                                    cursor: 'pointer',
                                    background: !config.isTransparent ? '#e6f7ff' : 'transparent',
                                    borderColor: !config.isTransparent ? '#1890ff' : '#d9d9d9'
                                  }}
                                >
                                  非透明背景
                                </div>
                                <div
                                  className={`button-item ${config.isTransparent ? 'selected' : ''}`}
                                  onClick={() => setConfig({ ...config, isTransparent: true })}
                                  style={{
                                    padding: '8px 16px',
                                    border: '1px solid #d9d9d9',
                                    borderRadius: '4px',
                                    cursor: 'pointer',
                                    background: config.isTransparent ? '#e6f7ff' : 'transparent',
                                    borderColor: config.isTransparent ? '#1890ff' : '#d9d9d9'
                                  }}
                                >
                                  透明背景
                                </div>
                              </div>
                            </div>

                            <div>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                <Text>数字人背景图URL</Text>
                                <Tooltip title="设置数字人背景图片URL">
                                  <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                </Tooltip>
                              </div>
                              <Input
                                placeholder="请输入背景图URL"
                                value={config.backgroundImageUrl}
                                onChange={(e) => setConfig({ ...config, backgroundImageUrl: e.target.value })}
                                style={{ marginBottom: '8px' }}
                              />
                              <div style={{ fontSize: '12px', color: '#666' }}>
                                <div>1.文件大小不超过 3 MB</div>
                                <div>2.支持的图片格式：png、jpg、jpeg、bmp</div>
                              </div>
                            </div>
                          </Space>
                        </Card>
                      </>
                    ),
                  },
                  {
                    key: 'advanced',
                    label: '展示配置',
                    children: (
                      <>
                        <Card
                          title={<Space><SettingOutlined /> 关键词展示配置</Space>}
                          style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                            <Text>项目素材库</Text>
                            <Tooltip title="上传项目展示素材图片视频">
                              <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                            </Tooltip>
                            <Button
                              type="primary"
                              size="small"
                              style={{ marginLeft: '8px' }}
                              onClick={() => setIsUploadModalVisible(true)}
                            >
                              上传图片视频
                            </Button>
                          </div>
                          <div style={{ marginBottom: '16px' }}>
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                              <Text>页面背景：</Text>
                              <Select
                                style={{ width: '200px', marginLeft: '8px' }}
                                value={config.defaultBackgroundUrl || ''}
                                onChange={(value) => setConfig({ ...config, defaultBackgroundUrl: value })}
                                placeholder="请选择页面背景"
                              >
                                {uploadedBackgrounds.map(bg => (
                                  <Option key={bg.id} value={bg.url}>
                                    {bg.name}
                                  </Option>
                                ))}
                              </Select>
                              {config.defaultBackgroundUrl && (
                                <Button
                                  type="text"
                                  icon={<MinusCircleOutlined />}
                                  onClick={() => setConfig({ ...config, defaultBackgroundUrl: '' })}
                                  style={{ marginLeft: '8px' }}
                                />
                              )}
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px' }}>
                              <Switch
                                checked={config.autoResetBackground}
                                onChange={(checked) => setConfig({ ...config, autoResetBackground: checked })}
                              />
                              <Text style={{ marginLeft: '8px' }}>讲述完关键词内容后自动切换回默认展示</Text>
                              <Tooltip title="开启后，图片类型将在指定时间后自动切换，视频类型将在播放完成后自动切换">
                                <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                              </Tooltip>
                            </div>

                            {config.autoResetBackground && (
                              <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px', marginLeft: '24px' }}>
                                <Text>图片展示时间（秒）：</Text>
                                <Slider
                                  min={1}
                                  max={30}
                                  value={config.keywordDisplayDuration}
                                  onChange={(value) => setConfig({ ...config, keywordDisplayDuration: value })}
                                  style={{ width: '200px', marginLeft: '8px' }}
                                  marks={{
                                    1: '1s',
                                    5: '5s',
                                    10: '10s',
                                    30: '30s'
                                  }}
                                />
                              </div>
                            )}
                          </div>
                          {config.keywordBackgrounds?.map((bg, index) => (
                            <React.Fragment key={index}>
                              <div style={{ display: 'flex', gap: '8px', marginBottom: '8px' }}>
                                <Input
                                  placeholder="关键词"
                                  value={bg.keyword}
                                  onChange={(e) => {
                                    const newBackgrounds = [...(config.keywordBackgrounds || [])];
                                    newBackgrounds[index].keyword = e.target.value;
                                    setConfig({ ...config, keywordBackgrounds: newBackgrounds });
                                  }}
                                />
                                <Select
                                  style={{ width: '120px' }}
                                  value={bg.type}
                                  onChange={(value) => {
                                    const newBackgrounds = [...(config.keywordBackgrounds || [])];
                                    newBackgrounds[index].type = value as 'image' | 'video';
                                    setConfig({ ...config, keywordBackgrounds: newBackgrounds });
                                  }}
                                >
                                  <Option value="image">图片</Option>
                                  <Option value="video">视频</Option>
                                </Select>
                                <Select
                                  style={{ width: '200px' }}
                                  value={bg.url}
                                  onChange={(value) => {
                                    const newBackgrounds = [...(config.keywordBackgrounds || [])];
                                    newBackgrounds[index].url = value;
                                    setConfig({ ...config, keywordBackgrounds: newBackgrounds });
                                  }}
                                >
                                  {uploadedBackgrounds.map(bg => (
                                    <Option key={bg.id} value={bg.url}>
                                      {bg.name}
                                    </Option>
                                  ))}
                                </Select>
                                <Select
                                  style={{ width: '120px' }}
                                  value={bg.triggerType || 'aiReply'}
                                  onChange={(value) => {
                                    const newBackgrounds = [...(config.keywordBackgrounds || [])];
                                    newBackgrounds[index].triggerType = value as 'aiReply' | 'userInput' | 'both';
                                    setConfig({ ...config, keywordBackgrounds: newBackgrounds });
                                  }}
                                >
                                  <Option value="aiReply">AI回复</Option>
                                  <Option value="userInput">用户输入</Option>
                                  <Option value="both">AI和用户</Option>
                                </Select>
                                <Button
                                  icon={<MinusCircleOutlined />}
                                  onClick={() => {
                                    const newBackgrounds = (config.keywordBackgrounds || []).filter((_, i) => i !== index);
                                    setConfig({ ...config, keywordBackgrounds: newBackgrounds });
                                  }}
                                />
                              </div>
                              {(bg.triggerType === 'userInput' || bg.triggerType === 'both') && (
                                <div style={{ display: 'flex', gap: '8px', marginBottom: '16px', marginLeft: '20px' }}>
                                  <Input
                                    placeholder="关键词触发后的固定回复内容"
                                    value={bg.response || ''}
                                    onChange={(e) => {
                                      const newBackgrounds = [...(config.keywordBackgrounds || [])];
                                      newBackgrounds[index].response = e.target.value;
                                      setConfig({ ...config, keywordBackgrounds: newBackgrounds });
                                    }}
                                    style={{ width: '100%' }}
                                  />
                                </div>
                              )}
                            </React.Fragment>
                          ))}
                          <Button
                            icon={<PlusOutlined />}
                            onClick={() => {
                              setConfig({
                                ...config,
                                keywordBackgrounds: [
                                  ...(config.keywordBackgrounds || []),
                                  { keyword: '', type: 'image', url: '', triggerType: 'aiReply' }
                                ]
                              });
                            }}
                          >
                            添加关键词展示
                          </Button>

                          {/* 添加视频声音控制配置 */}
                          <div style={{ marginTop: '16px', borderTop: '1px dashed #d9d9d9', paddingTop: '16px' }}>
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                              <Text>视频声音控制</Text>
                              <Tooltip title="当用户说特定短语时，播放的视频才会有声音">
                                <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                              </Tooltip>
                            </div>
                            <div style={{ display: 'flex', alignItems: 'center', marginTop: '8px', marginBottom: '8px' }}>
                              <Switch
                                checked={config.videoSoundEnabled}
                                onChange={(checked) => setConfig({ ...config, videoSoundEnabled: checked })}
                              />
                              <Text style={{ marginLeft: '8px' }}>启用语音控制视频声音</Text>
                              <Tooltip title="开启后，仅当用户说出指定短语时，视频才会播放声音，其他时候视频静音">
                                <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                              </Tooltip>
                            </div>

                            {config.videoSoundEnabled && (
                              <div style={{ marginTop: '8px' }}>
                                <Text>播放视频声音的触发短语：</Text>
                                <div style={{ marginTop: '8px', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                  {(config.videoPlayPhrases || []).map((phrase, index) => (
                                    <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '8px', justifyContent: 'center' }}>
                                      <Input
                                        value={phrase}
                                        onChange={(e) => {
                                          const newPhrases = [...(config.videoPlayPhrases || [])];
                                          newPhrases[index] = e.target.value;
                                          setConfig({ ...config, videoPlayPhrases: newPhrases });
                                        }}
                                        placeholder="请输入触发短语"
                                        style={{ width: '200px', marginRight: '8px' }}
                                      />
                                      <Button
                                        type="text"
                                        danger
                                        icon={<MinusCircleOutlined />}
                                        onClick={() => {
                                          const newPhrases = (config.videoPlayPhrases || []).filter((_, i) => i !== index);
                                          setConfig({ ...config, videoPlayPhrases: newPhrases });
                                        }}
                                      />
                                    </div>
                                  ))}
                                  <Button
                                    type="dashed"
                                    onClick={() => {
                                      setConfig({
                                        ...config,
                                        videoPlayPhrases: [...(config.videoPlayPhrases || []), '']
                                      });
                                    }}
                                    icon={<PlusOutlined />}
                                  >
                                    添加触发短语
                                  </Button>
                                </div>
                              </div>
                            )}
                          </div>
                        </Card>

                        <Card
                          title={<Space><SettingOutlined /> 轮播展示配置</Space>}
                          style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                <Text>启用轮播展示</Text>
                                <Tooltip title="开启后，将按照设定的间隔时间轮播展示选定的素材">
                                  <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                </Tooltip>
                              </div>
                              <Switch
                                checked={config.carouselEnabled}
                                onChange={(checked) => setConfig({ ...config, carouselEnabled: checked })}
                              />
                              <Text style={{ marginLeft: '8px' }}>启用轮播展示</Text>
                            </div>

                            {config.carouselEnabled && (
                              <>
                                <div>
                                  <Text>轮播间隔时间（秒）：</Text>
                                  <Slider
                                    min={1}
                                    max={60}
                                    value={config.carouselInterval}
                                    onChange={(value) => setConfig({ ...config, carouselInterval: value })}
                                    style={{ marginTop: '8px' }}
                                    marks={{
                                      1: '1s',
                                      10: '10s',
                                      30: '30s',
                                      60: '60s'
                                    }}
                                  />
                                </div>

                                <div>
                                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                    <Text>关键词触发时暂停轮播</Text>
                                    <Tooltip title="开启后，当关键词触发展示时会暂停轮播，关键词展示结束后自动恢复轮播">
                                      <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                    </Tooltip>
                                  </div>
                                  <Switch
                                    checked={config.carouselPauseOnKeyword}
                                    onChange={(checked) => setConfig({ ...config, carouselPauseOnKeyword: checked })}
                                  />
                                  <Text style={{ marginLeft: '8px' }}>关键词触发时暂停轮播</Text>
                                </div>

                                <div>
                                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                    <Text>视频播放完成后切换</Text>
                                    <Tooltip title="开启后，当轮播项目为视频时，会等待视频播放完成再切换到下一个项目，而不是按固定间隔时间切换">
                                      <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                    </Tooltip>
                                  </div>
                                  <Switch
                                    checked={config.carouselWaitForVideoEnd}
                                    onChange={(checked) => setConfig({ ...config, carouselWaitForVideoEnd: checked })}
                                  />
                                  <Text style={{ marginLeft: '8px' }}>视频播放完成后切换</Text>
                                </div>

                                <div>
                                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                    <Text>轮播素材列表</Text>
                                    <Tooltip title="拖拽调整轮播顺序，取消勾选可暂时禁用某个素材">
                                      <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                    </Tooltip>
                                  </div>

                                  <DndContext
                                    sensors={sensors}
                                    collisionDetection={closestCenter}
                                    onDragEnd={handleCarouselDragEnd}
                                  >
                                    <SortableContext
                                      items={config.carouselItems.map(item => item.id)}
                                      strategy={verticalListSortingStrategy}
                                    >
                                      {config.carouselItems.map((item) => {
                                        const background = uploadedBackgrounds.find(bg => bg.id === item.backgroundId);
                                        return (
                                          <SortableCarouselItem
                                            key={item.id}
                                            id={item.id}
                                            item={item}
                                            background={background}
                                            onToggle={toggleCarouselItem}
                                            onRemove={removeCarouselItem}
                                          />
                                        );
                                      })}
                                    </SortableContext>
                                  </DndContext>

                                  <div style={{ marginTop: '16px' }}>
                                    <Text>添加素材到轮播：</Text>
                                    <Select
                                      style={{ width: '100%', marginTop: '8px' }}
                                      placeholder="请选择要添加到轮播的素材"
                                      onChange={(value) => {
                                        if (value && !config.carouselItems.find(item => item.backgroundId === value)) {
                                          addCarouselItem(value);
                                        }
                                      }}
                                      value={undefined}
                                    >
                                      {uploadedBackgrounds
                                        .filter(bg => !config.carouselItems.find(item => item.backgroundId === bg.id))
                                        .map(bg => (
                                          <Option key={bg.id} value={bg.id}>
                                            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                              <span>{bg.name}</span>
                                              <span style={{ color: '#666', fontSize: '12px' }}>
                                                ({bg.type === 'image' ? '图片' : '视频'})
                                              </span>
                                            </div>
                                          </Option>
                                        ))}
                                    </Select>
                                  </div>
                                </div>
                              </>
                            )}
                          </Space>
                        </Card>

                        <Card
                          title={<Space><SettingOutlined /> 数字人语音配置</Space>}
                          style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                <Text>语音唤醒</Text>
                                <Tooltip title="开启后，数字人只有在听到唤醒词后才会开始对话">
                                  <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                </Tooltip>
                              </div>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                <Switch
                                  checked={config.voiceWakeupEnabled}
                                  onChange={(checked) => setConfig({ ...config, voiceWakeupEnabled: checked })}
                                />
                                <Text style={{ marginLeft: '8px' }}>启用语音唤醒</Text>
                              </div>
                              {config.voiceWakeupEnabled && (
                                <>
                                  <div style={{ marginBottom: '8px' }}>
                                    <Text>唤醒词列表：</Text>
                                    <div style={{ marginTop: '8px', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                      {(config.voiceWakeupPhrases || []).map((phrase, index) => (
                                        <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '8px', justifyContent: 'center' }}>
                                          <Input
                                            value={phrase}
                                            onChange={(e) => {
                                              const newPhrases = [...(config.voiceWakeupPhrases || [])];
                                              newPhrases[index] = e.target.value;
                                              setConfig({ ...config, voiceWakeupPhrases: newPhrases });
                                            }}
                                            placeholder="请输入唤醒词"
                                            style={{ width: '200px', marginRight: '8px' }}
                                          />
                                          <Button
                                            type="text"
                                            danger
                                            icon={<MinusCircleOutlined />}
                                            onClick={() => {
                                              const newPhrases = (config.voiceWakeupPhrases || []).filter((_, i) => i !== index);
                                              setConfig({ ...config, voiceWakeupPhrases: newPhrases });
                                            }}
                                          />
                                        </div>
                                      ))}
                                      <Button
                                        type="dashed"
                                        onClick={() => {
                                          setConfig({
                                            ...config,
                                            voiceWakeupPhrases: [...(config.voiceWakeupPhrases || []), '']
                                          });
                                        }}
                                        icon={<PlusOutlined />}
                                      >
                                        添加唤醒词
                                      </Button>
                                    </div>
                                  </div>
                                  <div style={{ marginBottom: '8px' }}>
                                    <Text>唤醒超时时间（秒）：</Text>
                                    <Input
                                      type="number"
                                      value={config.voiceWakeupTimeout}
                                      onChange={(e) => setConfig({ ...config, voiceWakeupTimeout: parseInt(e.target.value) || 30 })}
                                      min={1}
                                      max={300}
                                      style={{ width: '100px', marginLeft: '8px' }}
                                    />
                                  </div>
                                </>
                              )}
                            </div>
                            <div>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                <Text>允许打断</Text>
                                <Tooltip title="开启后，用户可以在数字人说话时打断对话">
                                  <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                </Tooltip>
                              </div>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                <Switch
                                  checked={config.allowInterruption}
                                  onChange={(checked) => setConfig({ ...config, allowInterruption: checked })}
                                />
                                <Text style={{ marginLeft: '8px' }}>允许打断数字人说话</Text>
                              </div>
                              {config.allowInterruption && (
                                <div style={{ marginBottom: '8px' }}>
                                  <Text>打断关键词列表：</Text>
                                  <div style={{ marginTop: '8px', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                    {(config.interruptionKeywords || []).map((keyword, index) => (
                                      <div key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '8px', justifyContent: 'center' }}>
                                        <Input
                                          value={keyword}
                                          onChange={(e) => {
                                            const newKeywords = [...(config.interruptionKeywords || [])];
                                            newKeywords[index] = e.target.value;
                                            setConfig({ ...config, interruptionKeywords: newKeywords });
                                          }}
                                          placeholder="请输入打断关键词"
                                          style={{ width: '200px', marginRight: '8px' }}
                                        />
                                        <Button
                                          type="text"
                                          danger
                                          icon={<MinusCircleOutlined />}
                                          onClick={() => {
                                            const newKeywords = (config.interruptionKeywords || []).filter((_, i) => i !== index);
                                            setConfig({ ...config, interruptionKeywords: newKeywords });
                                          }}
                                        />
                                      </div>
                                    ))}
                                    <Button
                                      type="dashed"
                                      onClick={() => {
                                        setConfig({
                                          ...config,
                                          interruptionKeywords: [...(config.interruptionKeywords || []), '']
                                        });
                                      }}
                                      icon={<PlusOutlined />}
                                    >
                                      添加打断关键词
                                    </Button>
                                  </div>
                                </div>
                              )}
                            </div>
                            <div>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                <Text>显示输入框</Text>
                                <Tooltip title="开启后，将显示文本输入框">
                                  <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                </Tooltip>
                              </div>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                <Switch
                                  checked={config.showInputField}
                                  onChange={(checked) => setConfig({ ...config, showInputField: checked })}
                                />
                                <Text style={{ marginLeft: '8px' }}>显示文本输入框</Text>
                              </div>
                            </div>
                          </Space>
                        </Card>
                      </>
                    ),
                  },
                  {
                    key: 'ai',
                    label: '秘钥配置',
                    children: (
                      <>
                        <Card
                          title={<Space><SettingOutlined /> API 设置</Space>}
                          style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div>
                              <Text>AI API 地址:</Text>
                              <Input
                                value={config.openaiApiUrl}
                                onChange={(e) => setConfig({ ...config, openaiApiUrl: e.target.value })}
                                placeholder="请输入 AI API 地址"
                                style={{ marginTop: '8px' }}
                              />
                            </div>
                            <div>
                              <Text>AI API 密钥:</Text>
                              <Input.Password
                                value={config.openaiApiKey}
                                onChange={(e) => setConfig({ ...config, openaiApiKey: e.target.value })}
                                placeholder="请输入 AI API 密钥"
                                style={{ marginTop: '8px' }}
                              />
                            </div>
                            <div>
                              <Text>模型选择:</Text>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                <Text>模型选择</Text>
                                <Tooltip title="可以从预设选项中选择，也可以输入自定义的模型名称">
                                  <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                </Tooltip>
                              </div>
                              <Select
                                value={[config.openaiModel]}
                                onChange={(value) => {
                                  // 确保值是数组
                                  const modelArray = Array.isArray(value) ? value : [value];
                                  // 只取第一个值
                                  const model = modelArray[0] || 'doubao-1-5-pro-32k-250115';
                                  setConfig({ ...config, openaiModel: model });
                                }}
                                style={{ width: '100%', marginTop: '8px' }}
                                mode="tags"
                                placeholder="请选择或输入模型名称"
                                maxTagCount={1}
                                maxTagTextLength={50}
                                tokenSeparators={[',']}
                                allowClear
                                showSearch
                                optionFilterProp="children"
                                notFoundContent="没有找到匹配的模型"
                              >
                                <Option value="doubao-1-5-pro-32k-250115">豆包 Pro 32K</Option>
                              </Select>
                            </div>
                            <div>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px', marginTop: '16px' }}>
                                <Text>最大生成字符数</Text>
                                <Tooltip title="设置AI每次回复最多生成多少token，较小的值可以让回复更简短">
                                  <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                </Tooltip>
                              </div>
                              <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
                                <Slider
                                  min={10}
                                  max={1000}
                                  step={10}
                                  value={config.max_tokens}
                                  onChange={(value) => setConfig({ ...config, max_tokens: value })}
                                  style={{ flex: 1 }}
                                  marks={{
                                    10: '10',
                                    100: '100',
                                    500: '500',
                                    1000: '1000'
                                  }}
                                />
                                <InputNumber
                                  min={10}
                                  max={4000}
                                  value={config.max_tokens}
                                  onChange={(value) => setConfig({ ...config, max_tokens: value || 100 })}
                                  style={{ width: '100px' }}
                                />
                              </div>
                            </div>
                          </Space>
                        </Card>

                        <Card
                          title={<Space><SettingOutlined /> 知识库配置</Space>}
                          style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                              <Switch
                                checked={config.knowledgeBaseEnabled}
                                onChange={(checked) => setConfig({ ...config, knowledgeBaseEnabled: checked })}
                              />
                              <Text style={{ marginLeft: '8px' }}>启用知识库</Text>
                              <Tooltip title="启用后，数字人将能够从知识库中获取信息回答问题">
                                <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                              </Tooltip>
                            </div>
                            {config.knowledgeBaseEnabled && (
                              <>
                                <div>
                                  <Text>知识库 API 地址:</Text>
                                  <Input
                                    value={config.knowledgeBaseApiUrl}
                                    onChange={(e) => setConfig({ ...config, knowledgeBaseApiUrl: e.target.value })}
                                    placeholder="请输入知识库 API 地址"
                                    style={{ marginTop: '8px' }}
                                  />
                                </div>
                                <div>
                                  <Text>知识库 API 密钥:</Text>
                                  <Input.Password
                                    value={config.knowledgeBaseApiKey}
                                    onChange={(e) => setConfig({ ...config, knowledgeBaseApiKey: e.target.value })}
                                    placeholder="请输入知识库 API 密钥"
                                    style={{ marginTop: '8px' }}
                                  />
                                </div>
                              </>
                            )}
                          </Space>
                        </Card>

                        {config.knowledgeBaseEnabled && config.knowledgeBaseApiKey && config.knowledgeBaseApiUrl && (
                          <KnowledgeBaseManager
                            apiKey={config.knowledgeBaseApiKey}
                            apiUrl={config.knowledgeBaseApiUrl}
                          />
                        )}

                        <Card
                          title={<Space><SettingOutlined /> 提示词配置</Space>}
                          style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                <Text>系统提示词</Text>
                                <Tooltip title="设置AI的系统提示词，控制AI回复的行为和风格">
                                  <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                </Tooltip>
                              </div>
                              <Input.TextArea
                                value={config.systemPrompt}
                                onChange={(e) => setConfig({ ...config, systemPrompt: e.target.value })}
                                placeholder="请输入系统提示词，控制AI的回复风格和行为"
                                autoSize={{ minRows: 4, maxRows: 8 }}
                                style={{ marginTop: 8 }}
                              />
                            </div>
                          </Space>
                        </Card>

                        <Card
                          title={<Space><SoundOutlined /> 阿里云语音识别配置</Space>}
                          style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                              <Switch
                                checked={config.aliASREnabled}
                                onChange={(checked) => setConfig({ ...config, aliASREnabled: checked })}
                              />
                              <Text style={{ marginLeft: '8px' }}>启用阿里云语音识别</Text>
                              <Tooltip title="启用后，将使用阿里云实时语音识别服务代替浏览器原生语音识别，提供更高的准确率">
                                <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                              </Tooltip>
                            </div>

                            {config.aliASREnabled && (
                              <>
                                <div>
                                  <Text>阿里云语音识别 API 密钥:</Text>
                                  <Input.Password
                                    value={config.aliASRApiKey}
                                    onChange={(e) => setConfig({ ...config, aliASRApiKey: e.target.value })}
                                    placeholder="请输入阿里云语音识别 API 密钥"
                                    style={{ marginTop: '8px' }}
                                  />
                                </div>

                                <div style={{ marginTop: '16px' }}>
                                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                    <Text>采样率 (Hz)</Text>
                                    <Tooltip title="音频采样率，通常使用16000Hz">
                                      <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                    </Tooltip>
                                  </div>
                                  <Select
                                    value={config.aliASRSampleRate || 16000}
                                    onChange={(value) => setConfig({ ...config, aliASRSampleRate: value })}
                                    style={{ width: '100%' }}
                                  >
                                    <Option value={8000}>8000 Hz</Option>
                                    <Option value={16000}>16000 Hz</Option>
                                  </Select>
                                </div>

                                <div style={{ marginTop: '16px' }}>
                                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                    <Text>音频格式</Text>
                                    <Tooltip title="音频数据格式，默认为PCM">
                                      <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                    </Tooltip>
                                  </div>
                                  <Select
                                    value={config.aliASRFormat || 'wav'}
                                    onChange={(value) => setConfig({ ...config, aliASRFormat: value })}
                                    style={{ width: '100%' }}
                                  >
                                    <Option value="wav">WAV</Option>
                                    <Option value="pcm">PCM</Option>
                                  </Select>
                                </div>

                                <div style={{ marginTop: '16px', display: 'flex', alignItems: 'center' }}>
                                  <Switch
                                    checked={config.aliASREnablePunctuation !== false}
                                    onChange={(checked) => setConfig({ ...config, aliASREnablePunctuation: checked })}
                                  />
                                  <Text style={{ marginLeft: '8px' }}>启用标点符号</Text>
                                  <Tooltip title="识别结果中添加标点符号">
                                    <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                  </Tooltip>
                                </div>

                                <div style={{ marginTop: '16px', display: 'flex', alignItems: 'center' }}>
                                  <Switch
                                    checked={config.aliASREnableInterimResult !== false}
                                    onChange={(checked) => setConfig({ ...config, aliASREnableInterimResult: checked })}
                                  />
                                  <Text style={{ marginLeft: '8px' }}>启用中间结果</Text>
                                  <Tooltip title="实时返回识别的中间结果，提供更流畅的交互体验">
                                    <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                  </Tooltip>
                                </div>

                                <div style={{ marginTop: '16px' }}>
                                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: '8px' }}>
                                    <Text>结果超时时间 (毫秒)</Text>
                                    <Tooltip title="超过此时间没有新结果时，将当前结果标记为最终结果">
                                      <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                                    </Tooltip>
                                  </div>
                                  <InputNumber
                                    value={config.aliASRResultTimeoutMs}
                                    onChange={(value) => setConfig({ ...config, aliASRResultTimeoutMs: value || 1500 })}
                                    min={0}
                                    max={10000}
                                    step={500}
                                    style={{ width: '100%' }}
                                    placeholder="默认1500毫秒"
                                  />
                                  <div style={{ marginTop: '4px', fontSize: '12px', color: '#999' }}>
                                    设置为0表示禁用此功能，完全依赖服务端返回最终结果
                                  </div>
                                </div>
                              </>
                            )}
                          </Space>
                        </Card>

                        <Card
                          title={<Space><SettingOutlined /> 访问密码配置</Space>}
                          style={{
                            marginBottom: '24px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                            borderRadius: '8px'
                          }}
                        >
                          <Space direction="vertical" style={{ width: '100%' }}>
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
                              <Switch
                                checked={config.accessPasswordEnabled}
                                onChange={(checked) => setConfig({ ...config, accessPasswordEnabled: checked })}
                              />
                              <Text style={{ marginLeft: '8px' }}>启用访问密码</Text>
                              <Tooltip title="启用后，用户访问网站前需要先输入密码，否则不能加载数字人">
                                <InfoCircleOutlined style={{ marginLeft: '4px', color: '#5B647A' }} />
                              </Tooltip>
                            </div>

                            {config.accessPasswordEnabled && (
                              <div>
                                <Text>访问密码:</Text>
                                <Input.Password
                                  value={config.accessPassword}
                                  onChange={(e) => setConfig({ ...config, accessPassword: e.target.value })}
                                  placeholder="请输入访问密码"
                                  style={{ marginTop: '8px' }}
                                />
                                <div style={{ marginTop: '4px', fontSize: '12px', color: '#999' }}>
                                  设置访问密码后，用户需要输入正确密码才能访问数字人页面
                                </div>
                              </div>
                            )}
                          </Space>
                        </Card>
                      </>
                    ),
                  },
                ]}
              />
            </div>
          </Col>

          <Col span={16}>
            <Card
              title={<Space><VideoCameraOutlined /> 预览</Space>}
              extra={
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div style={{ display: 'flex', alignItems: 'center', gap: '16px', width: '100%', justifyContent: 'space-between' }}>
                    <Text type="secondary">数字人效果预览</Text>
                    {config.defaultBackgroundUrl && (
                      <Text type="secondary">
                        背景: {uploadedBackgrounds.find(bg => bg.url === config.defaultBackgroundUrl)?.name || '未知背景'}
                      </Text>
                    )}
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <Text type="secondary">位置:</Text>
                      <InputNumber
                        size="middle"
                        addonBefore="X"
                        addonAfter="%"
                        min={0}
                        max={100}
                        value={Math.max(0, Math.min(100, previewPosition.x))}
                        onChange={(value) => {
                          if (value !== null) {
                            setPreviewPosition({ ...previewPosition, x: value });
                            // 保存到配置
                            setConfig({ ...config, previewPosition: { ...previewPosition, x: value } });
                          }
                        }}
                        style={{ width: '130px' }}
                      />
                      <InputNumber
                        size="middle"
                        addonBefore="Y"
                        addonAfter="%"
                        min={0}
                        max={100}
                        value={Math.max(0, Math.min(100, previewPosition.y))}
                        onChange={(value) => {
                          if (value !== null) {
                            setPreviewPosition({ ...previewPosition, y: value });
                            // 保存到配置
                            setConfig({ ...config, previewPosition: { ...previewPosition, y: value } });
                          }
                        }}
                        style={{ width: '130px' }}
                      />
                    </div>
                    
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <Text type="secondary">尺寸:</Text>
                      <InputNumber
                        size="middle"
                        addonBefore="宽"
                        addonAfter="px"
                        min={100}
                        max={800}
                        value={Math.round(previewSize.width)}
                        onChange={(value) => {
                          if (value !== null) {
                            const newSize = { width: value, height: previewSize.height };
                            setPreviewSize(newSize);
                            // 保存到配置
                            setConfig({ ...config, previewSize: newSize });
                          }
                        }}
                        style={{ width: '130px' }}
                      />
                      <InputNumber
                        size="middle"
                        addonBefore="高"
                        addonAfter="px"
                        min={100}
                        max={800}
                        value={Math.round(previewSize.height)}
                        onChange={(value) => {
                          if (value !== null) {
                            const newSize = { width: previewSize.width, height: value };
                            setPreviewSize(newSize);
                            // 保存到配置
                            setConfig({ ...config, previewSize: newSize });
                          }
                        }}
                        style={{ width: '130px' }}
                      />
                      <Button 
                        type="primary"
                        size="middle"
                        onClick={() => {
                          const size = calculatePreviewSize();
                          setPreviewSize(size);
                          setConfig({ ...config, previewSize: size });
                        }}
                      >
                        重置
                      </Button>
                    </div>
                  </div>
                </Space>
              }
              style={{
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                borderRadius: '8px'
              }}
            >
              <div
                ref={containerRef}
                className="preview-container"
                style={{
                  height: '600px',
                  position: 'relative',
                  overflow: 'hidden',
                  background: '#f0f0f0',
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center'
                }}
              >
                {/* 显示选中的背景 */}
                {config.defaultBackgroundUrl && (
                  <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    width: '100%',
                    height: '100%',
                    zIndex: 0
                  }}>
                    {uploadedBackgrounds.find(bg => bg.url === config.defaultBackgroundUrl)?.type === 'video' ? (
                      <video
                        key={config.defaultBackgroundUrl}
                        src={config.defaultBackgroundUrl}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover'
                        }}
                        autoPlay
                        loop
                        muted
                      />
                    ) : (
                      <img
                        src={config.defaultBackgroundUrl}
                        alt="背景"
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover'
                        }}
                      />
                    )}
                  </div>
                )}
                <div className={`virtual-bround-area ${config.outputAspectRatio === '9:16' ? 'portrait' : 'landscape'}`} style={{
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  width: '100%',
                  height: '100%',
                  position: 'relative',
                  zIndex: 1
                }}>
                  <Draggable
                    nodeRef={draggableRef}
                    position={calculateActualPosition(previewPosition)}
                    onStart={() => setIsDragging(true)}
                    onStop={() => setIsDragging(false)}
                    bounds="parent"
                  >
                    <div
                      ref={draggableRef}
                      style={{
                        position: 'absolute',
                        cursor: isDragging ? 'grabbing' : 'grab',
                        touchAction: 'none',
                        transform: 'translate(-50%, -50%)'
                      }}
                    >
                      <ResizableBox
                        width={previewSize.width}
                        height={previewSize.height}
                        minConstraints={[200, 200]}
                        maxConstraints={[800, 800]}
                        resizeHandles={['n', 's', 'e', 'w', 'ne', 'nw', 'se', 'sw']}
                        style={{
                          background: 'transparent',
                          border: '2px dashed #1890ff',
                          boxSizing: 'border-box',
                          position: 'relative'
                        }}
                      >
                        <div className="cropper-container" style={{
                          width: '100%',
                          height: '100%',
                          display: 'flex',
                          justifyContent: 'center',
                          alignItems: 'center'
                        }}>
                          <div className="cropper-image" style={{
                            backgroundImage: `url(${selectedFigure.templateImg})`,
                            inset: '0px',
                            width: '100%',
                            height: '100%',
                            backgroundSize: 'contain',
                            backgroundPosition: 'center',
                            backgroundRepeat: 'no-repeat'
                          }}></div>
                        </div>
                      </ResizableBox>
                    </div>
                  </Draggable>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </Content>

      {/* 形象选择弹框 */}
      <Modal
        title="选择形象"
        open={showFigureModal}
        onCancel={() => setShowFigureModal(false)}
        width={1000}
        footer={null}
        style={{ top: 20 }}
        styles={{
          body: {
            padding: '24px',
            maxHeight: 'calc(100vh - 200px)',
            overflow: 'auto'
          }
        }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Row gutter={[16, 16]}>
            <Col span={4}>
              <Select
                value={selectedGender}
                onChange={setSelectedGender}
                style={{ width: '100%' }}
              >
                <Option value="ALL_GENDER">全部性别</Option>
                <Option value="MALE">男</Option>
                <Option value="FEMALE">女</Option>
              </Select>
            </Col>
            <Col span={5}>
              <Select
                value={selectedStyle}
                onChange={setSelectedStyle}
                style={{ width: '100%' }}
              >
                <Option value="ALL_STYLE">全部风格</Option>
                <Option value="DAILY_LEISURE_STYLE">日常休闲</Option>
                <Option value="PROFESSIONAL_BUSINESS_STYLE">职业商务</Option>
              </Select>
            </Col>
            <Col span={5}>
              <Select
                value={selectedPose}
                onChange={setSelectedPose}
                style={{ width: '100%' }}
              >
                <Option value="ALL_POSE">全部姿态&机位</Option>
                <Option value="SEATED_HALF_BODY_CAMERA">坐姿半身</Option>
                <Option value="STANDING_HALF_BODY_CAMERA">站姿半身</Option>
                <Option value="STANDING_FULL_BODY_CAMERA">站姿全身</Option>
              </Select>
            </Col>
            <Col span={5}>
              <Select
                value={selectedBackground}
                onChange={setSelectedBackground}
                style={{ width: '100%' }}
              >
                <Option value="ALL_BACKGROUND">全部背景</Option>
                <Option value="TRANSPARENT_BACKGROUND">透明</Option>
                <Option value="INDOOR_BACKGROUND">室内</Option>
              </Select>
            </Col>
            <Col span={5}>
              <Select
                value={selectedAction}
                onChange={setSelectedAction}
                style={{ width: '100%' }}
              >
                <Option value="ALL_ACTION">全部动作</Option>
                <Option value="WITH_ACTION">带动作</Option>
                <Option value="WITHOUT_ACTION">不带动作</Option>
              </Select>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            {FIGURES.filter(figure => {
              const genderMatch = selectedGender === 'ALL_GENDER' ||
                (selectedGender === 'MALE' && figure.gender === 1) ||
                (selectedGender === 'FEMALE' && figure.gender === 0);
              const styleMatch = selectedStyle === 'ALL_STYLE' || figure.tags.includes(selectedStyle);
              const poseMatch = selectedPose === 'ALL_POSE' || figure.tags.includes(selectedPose);
              const backgroundMatch = selectedBackground === 'ALL_BACKGROUND' || figure.tags.includes(selectedBackground);
              const actionMatch = selectedAction === 'ALL_ACTION' ||
                (selectedAction === 'WITH_ACTION' && figure.tags.includes('WITH_ACTION')) ||
                (selectedAction === 'WITHOUT_ACTION' && !figure.tags.includes('WITH_ACTION'));
              return genderMatch && styleMatch && poseMatch && backgroundMatch && actionMatch;
            }).map(figure => (
              <Col span={6} key={figure.id}>
                <Card
                  hoverable
                  onClick={() => handleFigureSelect(figure.id.toString())}
                  style={{ border: figure.id.toString() === config.figureId ? '2px solid #1890ff' : 'none' }}
                  styles={{ body: { padding: '12px' } }}
                >
                  <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: '8px' }}>
                    <div style={{
                      width: '100%',
                      height: '240px',
                      overflow: 'hidden',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative'
                    }}
                      onMouseEnter={(e) => {
                        const button = e.currentTarget.querySelector('button');
                        if (button) button.style.opacity = '1';
                      }}
                      onMouseLeave={(e) => {
                        const button = e.currentTarget.querySelector('button');
                        if (button) button.style.opacity = '0';
                      }}
                    >
                      <img
                        alt={figure.name}
                        src={figure.templateImg}
                        style={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'contain'
                        }}
                      />
                      <Button
                        type="default"
                        size="small"
                        style={{
                          position: 'absolute',
                          bottom: '16px',
                          left: '50%',
                          transform: 'translateX(-50%)',
                          opacity: 0,
                          transition: 'opacity 0.3s',
                          background: 'rgba(255, 255, 255, 0.9)',
                          border: '1px solid #d9d9d9',
                          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
                        }}
                        onClick={(e) => {
                          e.stopPropagation();
                          navigator.clipboard.writeText(figure.id.toString());
                          message.success('ID已复制到剪贴板');
                        }}
                      >
                        复制ID
                      </Button>
                    </div>
                    <div style={{ textAlign: 'center', width: '100%' }}>
                      <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '4px' }}>
                        {figure.name}
                      </div>
                      <div style={{ color: '#666', fontSize: '12px' }}>
                        ID: {figure.id}
                      </div>
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </Space>
      </Modal>

      {/* 声音选择弹框 */}
      <Modal
        title="选择声音"
        open={showVoiceModal}
        onCancel={() => setShowVoiceModal(false)}
        width={1000}
        footer={null}
        style={{ top: 20 }}
        styles={{
          body: {
            padding: '24px',
            maxHeight: 'calc(100vh - 200px)',
            overflow: 'auto'
          }
        }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Select
                value={selectedVoiceGender}
                onChange={setSelectedVoiceGender}
                style={{ width: '100%' }}
              >
                <Option value="ALL_GENDER">全部性别</Option>
                <Option value="MALE">男</Option>
                <Option value="FEMALE">女</Option>
              </Select>
            </Col>
            <Col span={8}>
              <Select
                value={selectedVoiceAge}
                onChange={setSelectedVoiceAge}
                style={{ width: '100%' }}
              >
                <Option value="ALL_AGE">全部年龄</Option>
                <Option value="18-24岁">18-24岁</Option>
                <Option value="25-35岁">25-35岁</Option>
                <Option value="36-50岁">36-50岁</Option>
              </Select>
            </Col>
            <Col span={8}>
              <Select
                value={selectedVoiceStyle}
                onChange={setSelectedVoiceStyle}
                style={{ width: '100%' }}
              >
                <Option value="ALL_STYLE">全部风格</Option>
                <Option value="权威专业">权威专业</Option>
                <Option value="活力激情">活力激情</Option>
                <Option value="亲和陪伴">亲和陪伴</Option>
              </Select>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            {VOICES.filter(voice => {
              const genderMatch = selectedVoiceGender === 'ALL_GENDER' ||
                (selectedVoiceGender === 'MALE' && voice.gender === 0) ||
                (selectedVoiceGender === 'FEMALE' && voice.gender === 1);
              const ageMatch = selectedVoiceAge === 'ALL_AGE' ||
                voice.tagListDetail.includes(selectedVoiceAge);
              const styleMatch = selectedVoiceStyle === 'ALL_STYLE' ||
                voice.tagListDetail.includes(selectedVoiceStyle);
              return genderMatch && ageMatch && styleMatch;
            }).map(voice => (
              <Col span={6} key={voice.id}>
                <Card
                  hoverable
                  onClick={() => handleVoiceSelect(voice.per)}
                  style={{ border: voice.per === config.ttsPer ? '2px solid #1890ff' : 'none' }}
                  styles={{ body: { padding: '12px' } }}
                >
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <div
                        style={{
                          width: '60px',
                          height: '60px',
                          overflow: 'hidden',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0,
                          position: 'relative'
                        }}
                      >
                        <img
                          alt={voice.name}
                          src={voice.thumbnailUrl}
                          style={{
                            maxWidth: '100%',
                            maxHeight: '100%',
                            objectFit: 'contain'
                          }}
                        />
                      </div>
                      <div style={{ flex: 1 }}>
                        <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '4px' }}>
                          {voice.name}
                        </div>
                        <div style={{ color: '#666', fontSize: '12px', display: 'flex', alignItems: 'center', gap: '4px' }}>
                          ID: {voice.per}
                          <Button
                            type="text"
                            size="small"
                            icon={<CopyOutlined />}
                            onClick={(e) => {
                              e.stopPropagation();
                              navigator.clipboard.writeText(voice.per);
                              message.success('ID已复制到剪贴板');
                            }}
                            style={{ padding: '0 4px' }}
                          />
                        </div>
                      </div>
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                        {voice.tagListDetail.map((tag, index) => (
                          <span
                            key={index}
                            style={{
                              background: '#f0f0f0',
                              padding: '2px 6px',
                              borderRadius: '4px',
                              fontSize: '12px'
                            }}
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </Space>
      </Modal>

      {/* 背景上传弹框 */}
      <Modal
        title="上传图片视频"
        open={isUploadModalVisible}
        onCancel={() => {
          setIsUploadModalVisible(false);
          setCustomFileName('');
        }}
        footer={null}
        style={{ top: 20 }}
        styles={{
          body: {
            padding: '24px',
            maxHeight: 'calc(100vh - 200px)',
            overflow: 'auto'
          }
        }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text>自定义文件名（可选）：</Text>
            <Input
              placeholder="请输入自定义文件名（不包含扩展名）"
              value={customFileName}
              onChange={(e) => setCustomFileName(e.target.value)}
              style={{ marginTop: '8px' }}
            />
          </div>
          <Upload.Dragger
            multiple
            accept=".jpg,.jpeg,.png,.gif,.bmp,.webp,.svg,.tiff,.mp4,.mov,.webm,.ogg,.avi,.wmv,.3gp"
            beforeUpload={(file) => {
              const isLt200M = file.size / 1024 / 1024 < 200;
              if (!isLt200M) {
                message.error('文件大小不能超过200MB！');
                return Upload.LIST_IGNORE;
              }

              // 如果用户没有输入自定义文件名，才检查原始文件名长度
              const maxFileNameLength = 20; // 设置文件名最大长度
              if (!customFileName) {
                const fileName = file.name;
                if (fileName.length > maxFileNameLength) {
                  message.warning(`文件名过长（${fileName.length}字符），将自动截断到${maxFileNameLength}字符以确保正常播放`);
                }
              } else if (customFileName.length > maxFileNameLength) {
                // 如果用户输入了自定义文件名，检查自定义文件名长度
                message.warning(`自定义文件名过长（${customFileName.length}字符），将自动截断到${maxFileNameLength}字符以确保正常播放`);
              }

              return true;
            }}
            customRequest={async (options) => {
              const { file, onSuccess, onError } = options;
              try {

                // 如果设置了自定义文件名，则修改文件名
                let fileToUpload = file as File;
                if (customFileName) {
                  const ext = (file as File).name.split('.').pop();
                  fileToUpload = new File(
                    [file as File],
                    `${customFileName}.${ext}`,
                    { type: (file as File).type }
                  );
                }

                // 使用UploadService上传文件
                const uploadService = UploadService.getInstance();
                await uploadService.uploadBackground(fileToUpload);

                // 上传成功后重新加载背景列表
                await loadUploadedBackgrounds();

                onSuccess?.('ok');
                message.success('上传成功');
                setCustomFileName(''); // 重置自定义文件名
              } catch (error) {
                onError?.(error as Error);
                message.error('上传失败');
              } finally {
              }
            }}
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint" style={{ textAlign: 'left', paddingLeft: '20px' }}>
              支持单个或批量上传。<br/>
              支持图片格式: jpg、jpeg、png、gif、bmp、webp、svg、tiff<br/>
              支持视频格式: mp4、mov、webm、ogg、avi、wmv、3gp
            </p>
          </Upload.Dragger>

          <div style={{ marginTop: '16px' }}>
            <Title level={5}>已上传的背景</Title>
            <List
              grid={{ gutter: 16, column: 3 }}
              dataSource={uploadedBackgrounds}
              renderItem={item => (
                <List.Item>
                  <Card
                    hoverable
                    cover={
                      item.type === 'image' ? (
                        <div
                          style={{
                            height: 120,
                            overflow: 'hidden',
                            cursor: 'pointer'
                          }}
                          onClick={() => {
                            setPreviewUrl(item.url);
                            setPreviewVisible(true);
                          }}
                        >
                          <img
                            alt={item.name}
                            src={item.url}
                            style={{
                              width: '100%',
                              height: '100%',
                              objectFit: 'cover'
                            }}
                          />
                        </div>
                      ) : (
                        <video
                          src={item.url}
                          style={{
                            height: 120,
                            width: '100%',
                            objectFit: 'cover'
                          }}
                          controls
                          onError={(e) => {
                            console.error('视频加载失败:', item.url, e);
                            console.error('视频文件名:', item.name);
                          }}
                          onLoadStart={() => {
                            console.log('开始加载视频:', item.url);
                          }}
                        />
                      )
                    }
                    actions={[
                      <Button
                        type="text"
                        danger
                        onClick={async () => {
                          try {
                            const uploadService = UploadService.getInstance();
                            await uploadService.deleteBackground(item.id);
                            message.success('删除成功');
                            // 删除成功后重新加载背景列表
                            await loadUploadedBackgrounds();
                          } catch (error) {
                            message.error('删除失败');
                          }
                        }}
                      >
                        删除
                      </Button>
                    ]}
                  >
                    <Card.Meta
                      title={item.name}
                      description={item.type === 'image' ? '图片' : '视频'}
                    />
                  </Card>
                </List.Item>
              )}
            />
          </div>
        </Space>
      </Modal>

      {/* 图片预览弹窗 */}
      <Modal
        open={previewVisible}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width="80%"
        style={{ top: 20 }}
        styles={{
          body: {
            padding: '24px',
            textAlign: 'center'
          }
        }}
      >
        <img
          alt="预览图片"
          style={{ width: '100%' }}
          src={previewUrl}
        />
      </Modal>
    </Layout>
  );
};

export default ConfigPage;