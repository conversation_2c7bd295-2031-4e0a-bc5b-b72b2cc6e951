export interface KeywordBackground {
  keyword: string;
  type: 'image' | 'video';
  url: string;
  response?: string;
  triggerType?: 'aiReply' | 'userInput' | 'both';
}

export interface CarouselItem {
  id: string;
  backgroundId: string;
  order: number;
  enabled: boolean;
}

export interface DigitalHumanConfig {
  ttsPitch: number;
  ttsSpeed: number;
  ttsVolume: number;
  ttsPer: string;
  cameraId: string;
  figureId: string;
  autoChromaKey: boolean;
  showConfigButton: boolean;
  outputAspectRatio: '9:16' | '16:9';
  isTransparent: boolean;
  backgroundImageUrl: string;
  defaultBackgroundUrl: string;
  debugToolsEnabled: boolean;
  customParams: Array<{ key: string; value: string }>;
  openaiApiKey: string;
  openaiApiUrl: string;
  openaiModel: string;
  systemPrompt: string;
  max_tokens: number;
  knowledgeBaseApiKey: string;
  knowledgeBaseApiUrl: string;
  knowledgeBaseEnabled: boolean;
  keywordBackgrounds: Array<KeywordBackground>;
  previewPosition?: { x: number; y: number };
  previewSize?: { width: number; height: number };
  autoResetBackground: boolean;
  voiceWakeupEnabled: boolean;
  voiceWakeupPhrases: string[];
  voiceWakeupTimeout: number;
  allowInterruption: boolean;
  interruptionKeywords: string[];
  interruptionPhrases?: string[];
  similarityThreshold?: number;
  showInputField: boolean;
  carouselEnabled: boolean;
  carouselInterval: number;
  carouselItems: Array<CarouselItem>;
  carouselPauseOnKeyword: boolean;
  carouselWaitForVideoEnd: boolean;
  videoSoundEnabled: boolean;
  videoPlayPhrases: string[];
  keywordDisplayDuration: number;
  // 阿里云语音识别配置
  aliASREnabled?: boolean;
  aliASRApiKey?: string;
  aliASRSampleRate?: number;
  aliASRFormat?: string;
  aliASREnablePunctuation?: boolean;
  aliASREnableInterimResult?: boolean;
  aliASRResultTimeoutMs?: number; // 结果超时时间(毫秒)，超过此时间没有新结果时，将当前结果标记为最终结果
  // 自言自语过滤配置
  disableSelfTalkFilter?: boolean; // 是否禁用自言自语过滤功能
  selfTalkSimilarityThreshold?: number; // 自言自语检测的相似度阈值，默认0.6
  // 访问密码配置
  accessPasswordEnabled?: boolean; // 是否启用访问密码
  accessPassword?: string; // 访问密码
}