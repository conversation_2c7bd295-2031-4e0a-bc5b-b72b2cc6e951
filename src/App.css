#root {
    max-width: 100%;
    margin: 0;
    padding: 0;
    text-align: center;
    width: 100vw;
    height: 100vh;
    overflow: hidden;
}

.tip1 { 
    position: absolute;
    color: red;
    left: 0;
    cursor: pointer;
    top: 0;
    height: 100vh;
    width: 100vw;
    text-align: center;
    line-height: 100vh;
}

.tip2 {
    position: absolute;
    left: 50%;
    cursor: pointer;
    top: 25%;
    transform: translateX(-50%);
}

.button-wrapper {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    position: absolute;
    top: 10px;
    left: 10px;
    flex-wrap: wrap;
    z-index: 1000;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 8px;

    button {
        color: #FFF;
        background-color: #4CAF50;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    button:hover {
        background-color: #45a049;
    }
}

.iframe-wrapper {
    position: absolute;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: transparent;
}

.digital-human-container {
    position: relative;
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: transparent;
}

.chat-container {
    position: absolute;
    left: 3%;
    top: calc(15% + 70vh - 50px);
    width: 67%;
    max-height: 18%;
    background: transparent;
    padding: 16px 0 8px 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    overflow-y: auto;
    box-sizing: border-box;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
}

.message-row {
    width: 100%;
    display: flex;
    margin-bottom: 8px;
}

.user-row {
    justify-content: flex-end;
}

.ai-row {
    justify-content: flex-start;
}

.message {
    display: flex;
    flex-direction: column;
}

.message-content {
    max-width: 100%;
    padding: 10px 15px;
    border-radius: 15px;
    font-size: 15px;
    line-height: 1.5;
    word-break: break-all;
    text-align: left;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.message.user .message-content {
    background-color: rgba(0, 122, 255, 0.9);
    color: white;
    border-top-right-radius: 5px;
    border-top-left-radius: 15px;
    border-bottom-right-radius: 15px;
    border-bottom-left-radius: 15px;
}

.message.digitalHuman .message-content {
    background-color: rgba(233, 233, 235, 0.95);
    color: #000;
    border-top-left-radius: 5px;
    border-top-right-radius: 15px;
    border-bottom-right-radius: 15px;
    border-bottom-left-radius: 15px;
}

/* #digital-human-iframe {
    position: absolute;
    width: 140vw;
    height: 120vh;
    top: -20vh;
    left: -20vw;
} */

.mic-button-container {
    position: fixed;
    bottom: 100px;
    right: 30px;
    z-index: 1000;
}

.mic-button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.mic-button:hover {
    transform: scale(1.1);
    background-color: rgba(255, 255, 255, 1);
}

.mic-button.recording {
    background-color: rgba(244, 67, 54, 0.9);
    animation: pulse 1.5s infinite;
}

.mic-button.recording:hover {
    background-color: rgba(244, 67, 54, 1);
}

.mic-button.processing {
    background-color: rgba(128, 128, 128, 0.9);
    cursor: not-allowed;
}

.mic-button.processing:hover {
    transform: none;
    background-color: rgba(128, 128, 128, 0.9);
}

.mic-button.pulse {
    animation: micPulse 2s infinite;
}

.mic-icon {
    width: 30px;
    height: 30px;
    object-fit: contain;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 5px 15px rgba(244, 67, 54, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }
}

@keyframes micPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }
    50% {
        transform: scale(1.1);
        box-shadow: 0 0 0 15px rgba(0, 122, 255, 0.3);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }
}

.loading-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px 0;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    background-color: #666;
    border-radius: 50%;
    animation: loadingDots 1.4s infinite ease-in-out both;
}

.loading-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.loading-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

@keyframes loadingDots {
    0%, 80%, 100% { 
        transform: scale(0);
    } 
    40% { 
        transform: scale(1.0);
    }
}

.input-container {
    position: fixed;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    padding: 15px;
    border-radius: 20px;
    z-index: 1000;
    width: 80%;
    max-width: 600px;
    transition: bottom 0.3s ease;
}

.text-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    font-size: 16px;
    outline: none;
    transition: border-color 0.3s;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.text-input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.text-input:focus {
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.send-button {
    padding: 12px 24px;
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 16px;
    min-width: 80px;
}

.send-button:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.send-button:disabled {
    background-color: rgba(255, 255, 255, 0.1);
    cursor: not-allowed;
    transform: none;
}

.config-button {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.config-button:hover {
  background-color: #357abd;
}

.config-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.config-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.config-panel {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-panel h3 {
  margin: 0 0 16px 0;
  color: #333;
  text-align: center;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-item label {
  min-width: 100px;
  color: #666;
}

.config-item input[type="range"] {
  flex: 1;
}

.config-item span {
  min-width: 30px;
  text-align: right;
}

.config-item select {
  flex: 1;
  padding: 6px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.config-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
}

.config-buttons button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.config-buttons button:first-child {
  color: #333;
}

.config-buttons button:last-child {
  background-color: #4a90e2;
  color: white;
}

.config-buttons button:hover {
  opacity: 0.9;
}

.config-page {
  min-height: 100vh;
  padding: 20px;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #ddd;
}

.config-header h1 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.back-button {
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.back-button:hover {
  background-color: #357abd;
}

.config-section {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.config-section h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 15px;
}

.config-item:last-child {
  margin-bottom: 0;
}

.config-item label {
  min-width: 120px;
  color: #666;
  font-size: 14px;
}

.config-item input[type="range"] {
  flex: 1;
  height: 6px;
  -webkit-appearance: none;
  background: #ddd;
  border-radius: 3px;
  outline: none;
}

.config-item input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: #4a90e2;
  border-radius: 50%;
  cursor: pointer;
}

.config-item span {
  min-width: 30px;
  text-align: right;
  color: #666;
  font-size: 14px;
}

.config-item select {
  flex: 1;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  color: #333;
  background-color: white;
}

.config-item input[type="checkbox"] {
  margin-right: 8px;
}

.config-buttons {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.reset-button {
  padding: 12px 24px;
  background-color: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.reset-button:hover {
  background-color: #e5e5e5;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 16px;
  color: #666;
}

.error-message {
  background-color: #ffebee;
  color: #c62828;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 20px;
  text-align: center;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.save-button {
  padding: 8px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.save-button:hover {
  background-color: #45a049;
}

.save-button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.save-button[data-status="success"] {
  background-color: #4CAF50;
}

.save-button[data-status="error"] {
  background-color: #f44336;
}

/* 配置页面新样式 */
.config-page-main-area {
  display: flex;
  gap: 20px;
  padding: 20px;
  height: calc(100vh - 100px);
}

.left-panel {
  width: 400px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.middle-preview-area {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.config-item {
  margin-bottom: 24px;
}

.config-item-label {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 12px;
}

.figuer-card-wrapper,
.tts-card-wrapper {
  background: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  gap: 16px;
}

.figuer-card-thumbnail-box,
.tts-card-thumbnail-box {
  width: 120px;
  height: 160px;
  border-radius: 4px;
  overflow: hidden;
}

.figuer-card-thumbnail,
.tts-card-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.figuer-card-info,
.tts-card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.figuer-card-name,
.tts-card-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.figuer-card-id-box,
.tts-card-id-box {
  font-size: 14px;
  color: #666;
}

.config-sub-item {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-sub-item label {
  min-width: 100px;
  color: #666;
}

.config-sub-item input[type="range"] {
  flex: 1;
  height: 4px;
  -webkit-appearance: none;
  background: #ddd;
  border-radius: 2px;
  outline: none;
}

.config-sub-item input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: #4a90e2;
  border-radius: 50%;
  cursor: pointer;
}

.config-sub-item span {
  min-width: 30px;
  text-align: right;
  color: #666;
}

.config-sub-item input[type="checkbox"] {
  margin-right: 8px;
}

.virtual-bround-area-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.virtual-bround-area-title span {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.virtual-bround-area-desc {
  font-size: 14px;
  color: #666;
}

.virtual-bround-area {
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
}

.virtual-bround-area.portrait {
  aspect-ratio: 9/16;
  max-width: 400px;
  margin: 0 auto;
}

.draggable-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.cropper-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.cropper-image {
  position: absolute;
  width: 100%;
  height: 100%;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .config-page-main-area {
    flex-direction: column;
  }

  .left-panel {
    width: 100%;
  }

  .middle-preview-area {
    height: 500px;
  }
}

/* 弹框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 18px;
  color: #333;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 0;
  line-height: 1;
}

.modal-body {
  padding: 24px;
}

/* 旋转动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 形象和声音卡片网格 */
.figure-grid,
.voice-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  padding: 10px;
}

.figure-card,
.voice-card {
  background: #f5f7fa;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.figure-card:hover,
.voice-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.figure-card.selected,
.voice-card.selected {
  border-color: #4a90e2;
}

.figure-image,
.voice-image {
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.figure-image img,
.voice-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.figure-info,
.voice-info {
  padding: 12px;
}

.figure-name,
.voice-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.figure-id,
.voice-id {
  font-size: 14px;
  color: #666;
}

/* 可点击的卡片样式 */
.figuer-card-wrapper,
.tts-card-wrapper {
  cursor: pointer;
  transition: all 0.3s ease;
}

.figuer-card-wrapper:hover,
.tts-card-wrapper:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 隐藏滚动条 */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* 展示框样式 */
.display-box {
  position: absolute;
  top: 15%;
  left: 3%;
  width: 68%;
  height: 66%;
  border-radius: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  backdrop-filter: blur(5px);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.display-media {
  flex: 1;
  width: 100%;
  background-color: #000;
  position: relative;
  overflow: hidden;
}

.display-content {
  position: fixed;
  bottom: 30px;
  right: 30px;
  padding: 12px 24px;
  color: white;
  border-radius: 20px;
  font-size: 16px;
  line-height: 1.5;
  text-align: center;
  max-width: 300px;
  backdrop-filter: blur(8px);
  transition: all 0.3s ease;
  opacity: 0;
  animation: fadeIn 0.3s ease forwards;
  z-index: 1000;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}