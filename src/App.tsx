/* eslint-disable @typescript-eslint/no-explicit-any */
// 参考文献 https://developer.mozilla.org/en-US/docs/Web/Media/Autoplay_guide
import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import DigitalHuman from './components/DigitalHuman'
import ConfigPage from './components/ConfigPage'
import PasswordAuth from './components/PasswordAuth'
import { ConfigService } from './services/configService'
import './App.css'

function App() {
    const [isPasswordRequired, setIsPasswordRequired] = useState(false);
    const [isPasswordVerified, setIsPasswordVerified] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        checkPasswordRequirement();
    }, []);

    const checkPasswordRequirement = async () => {
        try {
            const configService = ConfigService.getInstance();
            const config = await configService.getConfig();

            // 检查是否启用了访问密码
            if (config.accessPasswordEnabled && config.accessPassword) {
                setIsPasswordRequired(true);
                // 检查是否已经验证过密码（仅在当前会话中有效）
                const verified = sessionStorage.getItem('passwordVerified') === 'true';
                setIsPasswordVerified(verified);
            } else {
                setIsPasswordRequired(false);
                setIsPasswordVerified(true);
            }
        } catch (error) {
            console.error('检查密码配置失败:', error);
            // 如果配置加载失败，默认不需要密码
            setIsPasswordRequired(false);
            setIsPasswordVerified(true);
        } finally {
            setIsLoading(false);
        }
    };

    const handlePasswordSuccess = () => {
        setIsPasswordVerified(true);
    };

    // 如果正在加载配置，显示加载状态
    if (isLoading) {
        return (
            <div style={{
                width: '100vw',
                height: '100vh',
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                background: 'linear-gradient(135deg, #e6eeff 0%, #d6e9ff 50%, #e6eeff 100%)'
            }}>
                <div style={{ textAlign: 'center' }}>
                    <div style={{
                        width: '50px',
                        height: '50px',
                        border: '5px solid rgba(102, 126, 234, 0.3)',
                        borderRadius: '50%',
                        borderTop: '5px solid #667eea',
                        animation: 'spin 1s linear infinite',
                        margin: '0 auto 16px'
                    }}></div>
                    <div style={{ color: '#667eea', fontSize: '16px' }}>正在加载...</div>
                </div>
            </div>
        );
    }

    // 如果需要密码验证且未验证，显示密码输入页面
    if (isPasswordRequired && !isPasswordVerified) {
        return <PasswordAuth onSuccess={handlePasswordSuccess} />;
    }

    // 正常显示应用内容
    return (
        <Router>
            <Routes>
                <Route path="/" element={<DigitalHuman />} />
                <Route path="/config" element={<ConfigPage />} />
            </Routes>
        </Router>
    )
}

export default App
