{"name": "digital-human-server", "version": "1.0.0", "description": "数字人服务器", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon --exec ts-node index.ts", "build": "tsc"}, "dependencies": {"axios": "^1.9.0", "cors": "^2.8.5", "express": "^4.18.2", "form-data": "^4.0.0", "multer": "^1.4.5-lts.1", "node-fetch": "^2.7.0", "uuid": "^9.0.0", "ws": "^8.16.0"}, "devDependencies": {"@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/multer": "^1.4.7", "@types/node": "^18.15.11", "@types/node-fetch": "^2.6.11", "@types/uuid": "^9.0.1", "@types/ws": "^8.18.1", "nodemon": "^2.0.22", "ts-node": "^10.9.1", "typescript": "^5.0.4"}, "keywords": [], "author": "", "license": "ISC", "nodemonConfig": {"watch": ["src/**/*.ts"], "ignore": ["src/**/*.spec.ts", "node_modules"], "ext": "ts,json", "delay": "1000"}}