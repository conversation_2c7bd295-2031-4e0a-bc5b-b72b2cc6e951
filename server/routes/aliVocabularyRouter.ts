import express from 'express';
import axios from 'axios';

const router = express.Router();
const ALI_API_ENDPOINT = 'https://dashscope.aliyuncs.com/api/v1/services/audio/asr/customization';
const ALI_TASK_ENDPOINT = 'https://dashscope.aliyuncs.com/api/v1/tasks/';

// 创建热词表
router.post('/create', async (req, res) => {
  try {
    const { apiKey, prefix, vocabulary, targetModel } = req.body;

    if (!apiKey || !prefix || !vocabulary) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const response = await axios.post(ALI_API_ENDPOINT, {
      model: 'speech-biasing',
      input: {
        action: 'create_vocabulary',
        target_model: targetModel || 'paraformer-realtime-v2',
        prefix,
        vocabulary
      }
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    res.json(response.data);
  } catch (error: any) {
    console.error('创建热词表出错:', error?.response?.data || error.message);
    res.status(error?.response?.status || 500).json({
      error: error?.response?.data?.message || error.message
    });
  }
});

// 查询任务状态
router.get('/tasks/:taskId', async (req, res) => {
  try {
    const { taskId } = req.params;
    const apiKey = req.headers.authorization?.split('Bearer ')[1];

    if (!apiKey || !taskId) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const response = await axios.get(`${ALI_TASK_ENDPOINT}${taskId}`, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    res.json(response.data);
  } catch (error: any) {
    console.error('查询任务状态出错:', error?.response?.data || error.message);
    res.status(error?.response?.status || 500).json({
      error: error?.response?.data?.message || error.message
    });
  }
});

// 列出热词表
router.post('/list', async (req, res) => {
  try {
    const { apiKey, prefix, pageIndex, pageSize } = req.body;

    if (!apiKey) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const response = await axios.post(ALI_API_ENDPOINT, {
      model: 'speech-biasing',
      input: {
        action: 'list_vocabulary',
        prefix: prefix || null,
        page_index: pageIndex || 0,
        page_size: pageSize || 10
      }
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    res.json(response.data);
  } catch (error: any) {
    console.error('列出热词表出错:', error?.response?.data || error.message);
    res.status(error?.response?.status || 500).json({
      error: error?.response?.data?.message || error.message
    });
  }
});

// 查询热词表详情
router.post('/query', async (req, res) => {
  try {
    const { apiKey, vocabularyId } = req.body;

    if (!apiKey || !vocabularyId) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const response = await axios.post(ALI_API_ENDPOINT, {
      model: 'speech-biasing',
      input: {
        action: 'query_vocabulary',
        vocabulary_id: vocabularyId
      }
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    res.json(response.data);
  } catch (error: any) {
    console.error('查询热词表详情出错:', error?.response?.data || error.message);
    res.status(error?.response?.status || 500).json({
      error: error?.response?.data?.message || error.message
    });
  }
});

// 更新热词表
router.post('/update', async (req, res) => {
  try {
    const { apiKey, vocabularyId, vocabulary } = req.body;

    if (!apiKey || !vocabularyId || !vocabulary) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const response = await axios.post(ALI_API_ENDPOINT, {
      model: 'speech-biasing',
      input: {
        action: 'update_vocabulary',
        vocabulary_id: vocabularyId,
        vocabulary
      }
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    res.json(response.data);
  } catch (error: any) {
    console.error('更新热词表出错:', error?.response?.data || error.message);
    res.status(error?.response?.status || 500).json({
      error: error?.response?.data?.message || error.message
    });
  }
});

// 删除热词表
router.post('/delete', async (req, res) => {
  try {
    const { apiKey, vocabularyId } = req.body;

    if (!apiKey || !vocabularyId) {
      return res.status(400).json({ error: '缺少必要参数' });
    }

    const response = await axios.post(ALI_API_ENDPOINT, {
      model: 'speech-biasing',
      input: {
        action: 'delete_vocabulary',
        vocabulary_id: vocabularyId
      }
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    res.json(response.data);
  } catch (error: any) {
    console.error('删除热词表出错:', error?.response?.data || error.message);
    res.status(error?.response?.status || 500).json({
      error: error?.response?.data?.message || error.message
    });
  }
});

export default router;
