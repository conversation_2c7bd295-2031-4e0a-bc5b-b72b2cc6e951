import fs from 'fs';
import path from 'path';

export interface KeywordBackground {
  keyword: string;
  type: 'image' | 'video';
  url: string;
  response?: string;
  triggerType?: 'aiReply' | 'userInput' | 'both';
}

export interface CarouselItem {
  id: string;
  backgroundId: string;
  order: number;
  enabled: boolean;
}

export interface DigitalHumanConfig {
  ttsPitch: number;
  ttsSpeed: number;
  ttsVolume: number;
  ttsPer: string;
  cameraId: string;
  figureId: string;
  autoChromaKey: boolean;
  showConfigButton: boolean;
  outputAspectRatio: '9:16' | '16:9';
  isTransparent: boolean;
  backgroundImageUrl: string;
  defaultBackgroundUrl: string;
  debugToolsEnabled: boolean;
  customParams: Array<{ key: string; value: string }>;
  openaiApiKey: string;
  openaiApiUrl: string;
  openaiModel: string;
  systemPrompt: string;
  max_tokens: number;
  knowledgeBaseApiKey: string;
  knowledgeBaseApiUrl: string;
  knowledgeBaseEnabled: boolean;
  keywordBackgrounds: Array<KeywordBackground>;
  previewPosition?: { x: number; y: number };
  previewSize?: { width: number; height: number };
  autoResetBackground: boolean;
  voiceWakeupEnabled: boolean;
  voiceWakeupPhrases: string[];
  voiceWakeupTimeout: number;
  allowInterruption: boolean;
  interruptionKeywords: string[];
  showInputField: boolean;
  carouselEnabled: boolean;
  carouselInterval: number;
  carouselItems: Array<CarouselItem>;
  carouselPauseOnKeyword: boolean;
  carouselWaitForVideoEnd: boolean;
  videoSoundEnabled?: boolean;
  videoPlayPhrases?: string[];
  keywordDisplayDuration?: number;
  // 阿里云语音识别配置
  aliASREnabled: boolean;
  aliASRApiKey: string;
  aliASRSampleRate: number;
  aliASRFormat: string;
  aliASREnablePunctuation: boolean;
  aliASREnableInterimResult: boolean;
  aliASRResultTimeoutMs: number;
  // 访问密码配置
  accessPasswordEnabled?: boolean;
  accessPassword?: string;
}

export class ConfigService {
  private static instance: ConfigService;
  private configPath: string;
  private defaultConfig: DigitalHumanConfig;

  private constructor() {
    this.configPath = path.join(__dirname, '../data/config.json');
    this.defaultConfig = {
      ttsPitch: 5,
      ttsSpeed: 5,
      ttsVolume: 5,
      ttsPer: 'CAP_4193',
      cameraId: '2',
      figureId: '234340',
      autoChromaKey: true,
      showConfigButton: false,
      outputAspectRatio: '9:16',
      isTransparent: false,
      backgroundImageUrl: '',
      defaultBackgroundUrl: '',
      debugToolsEnabled: true,
      customParams: [
        { key: 'cp-preAlertSec', value: '120' }
      ],
      openaiApiKey: '',
      openaiApiUrl: 'https://oneapi.jingyuncenter.com/v1/chat/completions',
      openaiModel: 'doubao-1-5-pro-32k-250115',
      systemPrompt: '你是一个友好的数字人助手，请用简短自然的语言回答用户的问题。',
      max_tokens: 100,
      knowledgeBaseApiKey: '',
      knowledgeBaseApiUrl: '',
      knowledgeBaseEnabled: false,
      keywordBackgrounds: [],
      autoResetBackground: true,
      voiceWakeupEnabled: true,
      voiceWakeupPhrases: ['小金小金'],
      voiceWakeupTimeout: 300,
      allowInterruption: false,
      interruptionKeywords: ['小金小金'],
      showInputField: false,
      carouselEnabled: false,
      carouselInterval: 5,
      carouselItems: [],
      carouselPauseOnKeyword: true,
      carouselWaitForVideoEnd: false,
      videoSoundEnabled: false,
      videoPlayPhrases: ['播放视频', '打开视频声音'],
      keywordDisplayDuration: 5,
      // 阿里云语音识别配置
      aliASREnabled: false,
      aliASRApiKey: '',
      aliASRSampleRate: 16000,
      aliASRFormat: 'wav',
      aliASREnablePunctuation: true,
      aliASREnableInterimResult: true,
      aliASRResultTimeoutMs: 1500,
      // 访问密码配置
      accessPasswordEnabled: false,
      accessPassword: ''
    };
    this.ensureConfigFile();
  }

  public static getInstance(): ConfigService {
    if (!ConfigService.instance) {
      ConfigService.instance = new ConfigService();
    }
    return ConfigService.instance;
  }

  private ensureConfigFile(): void {
    try {
      if (!fs.existsSync(this.configPath)) {
        const dirPath = path.dirname(this.configPath);
        if (!fs.existsSync(dirPath)) {
          fs.mkdirSync(dirPath, { recursive: true });
        }
        fs.writeFileSync(this.configPath, JSON.stringify(this.defaultConfig, null, 2));
      }
    } catch (error) {
      console.error('创建配置文件失败:', error);
    }
  }

  private mergeConfig(savedConfig: any): DigitalHumanConfig {
    // 深度合并配置，确保所有字段都存在
    const mergedConfig = {
      ...this.defaultConfig,
      ...savedConfig,
      customParams: savedConfig.customParams || this.defaultConfig.customParams,
      keywordBackgrounds: savedConfig.keywordBackgrounds || this.defaultConfig.keywordBackgrounds,
      voiceWakeupPhrases: savedConfig.voiceWakeupPhrases || this.defaultConfig.voiceWakeupPhrases,
      interruptionKeywords: savedConfig.interruptionKeywords || this.defaultConfig.interruptionKeywords,
      previewPosition: savedConfig.previewPosition || this.defaultConfig.previewPosition,
      previewSize: savedConfig.previewSize || this.defaultConfig.previewSize,
      carouselItems: savedConfig.carouselItems || this.defaultConfig.carouselItems,
      videoPlayPhrases: savedConfig.videoPlayPhrases || this.defaultConfig.videoPlayPhrases
    };

    // 确保所有必需字段都有值
    Object.keys(this.defaultConfig).forEach(key => {
      if (mergedConfig[key as keyof DigitalHumanConfig] === undefined) {
        (mergedConfig as any)[key] = this.defaultConfig[key as keyof DigitalHumanConfig];
      }
    });

    return mergedConfig;
  }

  public async getConfig(): Promise<DigitalHumanConfig> {
    try {
      const data = await fs.promises.readFile(this.configPath, 'utf-8');
      const savedConfig = JSON.parse(data);
      return this.mergeConfig(savedConfig);
    } catch (error) {
      console.error('读取配置文件失败:', error);
      throw new Error('读取配置文件失败');
    }
  }

  public async saveConfig(config: DigitalHumanConfig): Promise<void> {
    try {
      // 合并配置，确保所有字段都存在
      const mergedConfig = this.mergeConfig(config);
      await fs.promises.writeFile(this.configPath, JSON.stringify(mergedConfig, null, 2));
    } catch (error) {
      console.error('保存配置文件失败:', error);
      throw new Error('保存配置文件失败');
    }
  }
}