import WebSocket from 'ws';
import { v4 as uuidv4 } from 'uuid';
import { ConfigService } from './configService';
import { EventEmitter } from 'events';

export interface SpeechRecognitionOptions {
  sampleRate?: number;
  format?: string;
  enablePunctuation?: boolean;
  enableInterimResult?: boolean;
  enableInverseTextNormalization?: boolean;
}

export interface SpeechRecognitionResult {
  text: string;
  isFinal: boolean;
  confidence?: number;
}

export class SpeechRecognitionService extends EventEmitter {
  private static instance: SpeechRecognitionService;
  private configService: ConfigService;
  private connections: Map<string, {
    ws: WebSocket;
    sessionId: string;
    clientWs: WebSocket;
    taskStarted: boolean;
    lastActivityTime: number;
  }> = new Map();

  private constructor() {
    super();
    this.configService = ConfigService.getInstance();

    // 启动心跳检查
    setInterval(() => this.checkConnections(), 3000);
  }

  public static getInstance(): SpeechRecognitionService {
    if (!SpeechRecognitionService.instance) {
      SpeechRecognitionService.instance = new SpeechRecognitionService();
    }
    return SpeechRecognitionService.instance;
  }

  /**
   * 创建一个新的语音识别会话
   * @param clientWs 客户端WebSocket连接
   * @param options 识别选项
   * @returns 会话ID
   */
  public createSession(clientWs: WebSocket, options: SpeechRecognitionOptions = {}): string {
    const sessionId = uuidv4();
    console.log(`创建语音识别会话: ${sessionId}`);

    this.setupClientWebSocket(sessionId, clientWs);

    return sessionId;
  }

  /**
   * 设置客户端WebSocket连接的事件处理
   * @param sessionId 会话ID
   * @param clientWs 客户端WebSocket连接
   */
  private setupClientWebSocket(sessionId: string, clientWs: WebSocket): void {
    console.log(`设置WebSocket事件处理: ${sessionId}`);

    // 处理客户端消息
    clientWs.on('message', async (message: Buffer) => {
      try {
        // 检查是否是控制消息
        if (message.length < 1000) { // 假设控制消息很小
          try {
            const controlMsg = JSON.parse(message.toString());
            console.log(`收到控制消息: ${sessionId}`, controlMsg);

            if (controlMsg.action === 'start') {
              // 启动语音识别
              await this.startRecognition(sessionId, clientWs, controlMsg.options || {});
            } else if (controlMsg.action === 'stop') {
              // 停止语音识别
              await this.stopRecognition(sessionId);
            }
          } catch (e) {
            // 不是JSON，当作音频数据处理
            await this.processAudioData(sessionId, message);
          }
        } else {
          // 音频数据
          await this.processAudioData(sessionId, message);
        }
      } catch (error) {
        console.error('处理客户端消息错误:', error);
        this.sendErrorToClient(clientWs, '处理音频数据失败');
      }
    });

    // 处理客户端断开连接
    clientWs.on('close', () => {
      console.log(`客户端连接关闭: ${sessionId}`);
      this.stopRecognition(sessionId);
    });

    clientWs.on('error', (error: Error) => {
      console.error(`客户端连接错误: ${sessionId}`, error);
      this.stopRecognition(sessionId);
    });
  }

  /**
   * 启动语音识别
   * @param sessionId 会话ID
   * @param clientWs 客户端WebSocket连接
   * @param options 识别选项
   */
  private async startRecognition(sessionId: string, clientWs: WebSocket, options: SpeechRecognitionOptions): Promise<void> {
    try {
      // 获取配置
      const config = await this.configService.getConfig();
      if (!config.aliASRApiKey) {
        console.error('未配置阿里云语音识别API密钥');
        this.sendErrorToClient(clientWs, '未配置阿里云语音识别API密钥，请在配置页面设置');
        return;
      }

      // 关闭现有连接
      if (this.connections.has(sessionId)) {
        await this.stopRecognition(sessionId);
      }

      console.log(`开始语音识别: ${sessionId}, 选项:`, options);

      // 生成任务ID
      const taskId = uuidv4().replace(/-/g, '').slice(0, 32);
      console.log(`生成任务ID: ${taskId}`);

      // 创建WebSocket连接
      console.log(`连接阿里云WebSocket: ${sessionId}`);
      const ws = new WebSocket('wss://dashscope.aliyuncs.com/api-ws/v1/inference/', {
        headers: {
          'Authorization': `bearer ${config.aliASRApiKey}`,
          'X-DashScope-DataInspection': 'enable'
        }
      });

      let taskStarted = false;

      // 存储连接信息
      this.connections.set(sessionId, {
        ws,
        sessionId,
        clientWs,
        taskStarted: false,
        lastActivityTime: Date.now()
      });

      // 设置WebSocket事件
      ws.on('open', () => {
        console.log(`阿里云WebSocket连接已打开: ${sessionId}`);

        // 发送run-task指令
        const runTaskMessage = {
          header: {
            action: 'run-task',
            task_id: taskId,
            streaming: 'duplex'
          },
          payload: {
            task_group: 'audio',
            task: 'asr',
            function: 'recognition',
            model: 'paraformer-realtime-v2',
            parameters: {
              sample_rate: options.sampleRate || 16000,
              format: options.format || 'wav',
              enable_punctuation: options.enablePunctuation !== false,
              enable_interim_result: options.enableInterimResult !== false,
              enable_inverse_text_normalization: options.enableInverseTextNormalization !== false
            },
            input: {}
          }
        };

        console.log(`发送run-task指令: ${sessionId}`, JSON.stringify(runTaskMessage));
        ws.send(JSON.stringify(runTaskMessage));
      });

      ws.on('message', (data: Buffer) => {
        try {
          const message = JSON.parse(data.toString());
          console.log(`收到阿里云WebSocket消息: ${sessionId}`, JSON.stringify(message));

          switch (message.header?.event) {
            case 'task-started':
              console.log(`语音识别任务开始: ${sessionId}`);
              taskStarted = true;

              // 更新连接状态
              const conn = this.connections.get(sessionId);
              if (conn) {
                conn.taskStarted = true;
              }

              // 通知客户端识别已开始
              this.sendEventToClient(clientWs, 'start', { success: true });
              break;

            case 'result-generated':
              // 处理识别结果
              const text = message.payload?.output?.sentence?.text;
              const isFinal = message.payload?.output?.sentence?.is_final === true;

              if (text) {
                console.log(`识别结果: ${sessionId}, 文本: ${text}, 最终: ${isFinal}`);

                // 发送结果到客户端
                this.sendResultToClient(clientWs, {
                  text,
                  isFinal,
                  confidence: message.payload?.output?.sentence?.confidence
                });

                // 更新最后活动时间
                const conn = this.connections.get(sessionId);
                if (conn) {
                  conn.lastActivityTime = Date.now();
                }
              }
              break;

            case 'task-finished':
              console.log(`语音识别任务完成: ${sessionId}`);
              this.sendEventToClient(clientWs, 'end', { success: true });
              this.closeConnection(sessionId);
              break;

            case 'task-failed':
              console.error(`语音识别任务失败: ${sessionId}`, message.header?.error_message);
              this.sendErrorToClient(clientWs, message.header?.error_message || '语音识别失败');
              this.closeConnection(sessionId);
              break;

            default:
              console.log(`未知事件: ${message.header?.event}`);
          }
        } catch (error) {
          console.error('解析阿里云WebSocket消息失败:', error);
        }
      });

      ws.on('error', (error: Error) => {
        console.error(`阿里云WebSocket错误: ${sessionId}`, error);
        this.sendErrorToClient(clientWs, '语音识别服务连接错误');
        this.closeConnection(sessionId);
      });

      ws.on('close', () => {
        console.log(`阿里云WebSocket连接关闭: ${sessionId}`);
        if (!taskStarted) {
          this.sendErrorToClient(clientWs, '语音识别服务连接关闭');
        }
        this.closeConnection(sessionId);
      });

      // 设置超时，如果一定时间内没有收到task-started事件，则关闭连接
      setTimeout(() => {
        const conn = this.connections.get(sessionId);
        if (conn && !conn.taskStarted) {
          console.error(`语音识别任务启动超时: ${sessionId}`);
          this.sendErrorToClient(clientWs, '语音识别服务启动超时');
          this.closeConnection(sessionId);
        }
      }, 10000); // 10秒超时

    } catch (error) {
      console.error('启动语音识别失败:', error);
      this.sendErrorToClient(clientWs, '启动语音识别失败');
    }
  }

  /**
   * 处理音频数据
   * @param sessionId 会话ID
   * @param audioData 音频数据
   */
  private async processAudioData(sessionId: string, audioData: Buffer): Promise<void> {
    const conn = this.connections.get(sessionId);
    if (!conn || !conn.taskStarted) {
      return;
    }

    try {
      // 发送音频数据到阿里云WebSocket
      conn.ws.send(audioData);

      // 更新最后活动时间
      conn.lastActivityTime = Date.now();
    } catch (error) {
      console.error('发送音频数据失败:', error);
      this.sendErrorToClient(conn.clientWs, '发送音频数据失败');
    }
  }

  /**
   * 停止语音识别
   * @param sessionId 会话ID
   */
  private async stopRecognition(sessionId: string): Promise<void> {
    const conn = this.connections.get(sessionId);
    if (!conn) {
      return;
    }

    try {
      if (conn.taskStarted) {
        // 发送finish-task指令
        const finishTaskMessage = {
          header: {
            action: 'finish-task',
            task_id: sessionId,
            streaming: 'duplex'
          },
          payload: {
            input: {}
          }
        };

        conn.ws.send(JSON.stringify(finishTaskMessage));
      }
    } catch (error) {
      console.error('停止语音识别失败:', error);
    } finally {
      // 关闭连接
      this.closeConnection(sessionId);
    }
  }

  /**
   * 关闭连接
   * @param sessionId 会话ID
   */
  private closeConnection(sessionId: string): void {
    const conn = this.connections.get(sessionId);
    if (!conn) {
      return;
    }

    try {
      console.log(`关闭连接: ${sessionId}`);

      // 发送finish-task指令
      if (conn.taskStarted && conn.ws.readyState === WebSocket.OPEN) {
        try {
          const finishTaskMessage = {
            header: {
              action: 'finish-task'
            },
            payload: {
              input: {}
            }
          };

          conn.ws.send(JSON.stringify(finishTaskMessage));
          console.log(`发送finish-task指令: ${sessionId}`);
        } catch (e) {
          console.error(`发送finish-task指令失败: ${sessionId}`, e);
        }
      }

      // 关闭阿里云WebSocket连接
      try {
        if (conn.ws.readyState !== WebSocket.CLOSED) {
          conn.ws.close();
          console.log(`关闭阿里云WebSocket连接: ${sessionId}`);
        }
      } catch (e) {
        console.error(`关闭阿里云WebSocket连接失败: ${sessionId}`, e);
      }

      // 通知客户端连接已关闭
      try {
        if (conn.clientWs.readyState === WebSocket.OPEN) {
          this.sendEventToClient(conn.clientWs, 'end', { success: true });
          console.log(`通知客户端连接已关闭: ${sessionId}`);
        }
      } catch (e) {
        console.error(`通知客户端连接已关闭失败: ${sessionId}`, e);
      }
    } catch (e) {
      console.error(`关闭连接出现错误: ${sessionId}`, e);
    } finally {
      // 从连接映射中删除
      this.connections.delete(sessionId);
      console.log(`连接已从映射中删除: ${sessionId}`);
    }
  }

  /**
   * 发送事件到客户端
   * @param clientWs 客户端WebSocket连接
   * @param event 事件名称
   * @param data 事件数据
   */
  private sendEventToClient(clientWs: WebSocket, event: string, data: any): void {
    try {
      clientWs.send(JSON.stringify({
        event,
        data
      }));
    } catch (error) {
      console.error('发送事件到客户端失败:', error);
    }
  }

  /**
   * 发送识别结果到客户端
   * @param clientWs 客户端WebSocket连接
   * @param result 识别结果
   */
  private sendResultToClient(clientWs: WebSocket, result: SpeechRecognitionResult): void {
    try {
      clientWs.send(JSON.stringify({
        event: 'result',
        data: result
      }));
    } catch (error) {
      console.error('发送识别结果到客户端失败:', error);
    }
  }

  /**
   * 发送错误到客户端
   * @param clientWs 客户端WebSocket连接
   * @param errorMessage 错误消息
   */
  private sendErrorToClient(clientWs: WebSocket, errorMessage: string): void {
    try {
      clientWs.send(JSON.stringify({
        event: 'error',
        data: {
          message: errorMessage
        }
      }));
    } catch (error) {
      console.error('发送错误到客户端失败:', error);
    }
  }

  /**
   * 检查连接状态，处理超时和发送心跳
   */
  private checkConnections(): void {
    const now = Date.now();

    // 遍历所有连接
    for (const [sessionId, conn] of this.connections.entries()) {
      // 检查最后活动时间，如果超过15秒无活动，发送心跳
      if (now - conn.lastActivityTime > 15000) {
        try {
          // 发送心跳到阿里云WebSocket
          if (conn.ws.readyState === WebSocket.OPEN) {
            console.log(`发送心跳: ${sessionId}`);

            // 发送符合阿里云API要求的心跳数据包
            // 使用一个有效的但极短的PCM音频数据包作为心跳
            const heartbeatData = Buffer.alloc(320); // 10ms的16kHz 16bit PCM数据
            conn.ws.send(heartbeatData);

            // 更新最后活动时间
            conn.lastActivityTime = now;
          }
        } catch (error) {
          console.error(`发送心跳失败: ${sessionId}`, error);
        }
      }

      // 如果超过90秒无活动，关闭连接
      if (now - conn.lastActivityTime > 90000) {
        console.log(`连接超时: ${sessionId}, 最后活动时间: ${new Date(conn.lastActivityTime).toISOString()}, 当前时间: ${new Date(now).toISOString()}`);
        this.sendErrorToClient(conn.clientWs, 'Response timeout!');
        this.closeConnection(sessionId);
      }
    }
  }
}