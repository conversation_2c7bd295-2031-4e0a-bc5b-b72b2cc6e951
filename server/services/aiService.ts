import { ConfigService } from './configService';

export class AIService {
  private static instance: AIService;
  private configService: ConfigService;

  private constructor() {
    this.configService = ConfigService.getInstance();
  }

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  public async chat(messages: any[], userInput: string): Promise<any> {
    try {
      // 获取AI配置
      const config = await this.configService.getConfig();
      
      if (!config.openaiApiKey) {
        throw new Error('未配置 AI API 密钥');
      }

      if (!config.openaiApiUrl) {
        throw new Error('未配置 AI API 地址');
      }

      // 构建完整的消息列表
      const fullMessages = [
        {
          role: "system",
          content: config.systemPrompt || "你是一个友好的数字人助手，请用简短自然的语言回答用户的问题。"
        },
        ...messages,
        {
          role: "user",
          content: userInput
        }
      ];

      // 代理转发到AI服务
      const response = await fetch(config.openaiApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.openaiApiKey}`
        },
        body: JSON.stringify({
          model: config.openaiModel,
          messages: fullMessages,
          temperature: 0.7,
          max_tokens: config.max_tokens || 100
        })
      });

      if (!response.ok) {
        throw new Error(`AI API调用失败: ${response.status}`);
      }

      const data = await response.json();
      return data;

    } catch (error) {
      console.error('AI服务调用失败:', error);
      throw error;
    }
  }

  // 新增流式处理方法，支持SSE格式输出
  public async streamChat(
    messages: any[], 
    userInput: string, 
    res: any
  ): Promise<void> {
    try {
      // 获取AI配置
      const config = await this.configService.getConfig();
      
      if (!config.openaiApiKey) {
        throw new Error('未配置 AI API 密钥');
      }

      if (!config.openaiApiUrl) {
        throw new Error('未配置 AI API 地址');
      }

      // 构建完整的消息列表
      const fullMessages = [
        {
          role: "system",
          content: config.systemPrompt || "你是一个友好的数字人助手，请用简短自然的语言回答用户的问题。"
        },
        ...messages,
        {
          role: "user",
          content: userInput
        }
      ];

      // 代理转发到AI服务
      const response = await fetch(config.openaiApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.openaiApiKey}`
        },
        body: JSON.stringify({
          model: config.openaiModel,
          messages: fullMessages,
          temperature: 0.7,
          max_tokens: config.max_tokens || 100,
          stream: true
        })
      });

      if (!response.ok) {
        throw new Error(`AI API调用失败: ${response.status}`);
      }

      // 获取响应体的流
      const reader = response.body?.getReader();
      const decoder = new TextDecoder();
      
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      let completeResponse = '';

      // 处理流式响应
      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          break;
        }
        
        // 解码二进制数据为文本
        const chunk = decoder.decode(value, { stream: true });
        
        // 解析数据块
        const lines = chunk
          .split('\n')
          .filter(line => line.trim() !== '' && line.trim() !== 'data: [DONE]');
        
        for (const line of lines) {
          // 确保行是以data:开头的
          if (line.startsWith('data:')) {
            try {
              const jsonData = line.replace(/^data: /, '').trim();
              if (jsonData) {
                const data = JSON.parse(jsonData);
                
                // 提取生成的内容
                if (data.choices && data.choices[0].delta && data.choices[0].delta.content) {
                  const content = data.choices[0].delta.content;
                  completeResponse += content;
                  
                  // 以SSE格式发送数据
                  res.write(`data: ${JSON.stringify({ 
                    choices: [{ message: { content: completeResponse } }],
                    partial: true
                  })}\n\n`);
                }
              }
            } catch (e) {
              console.error('解析流数据失败:', e);
            }
          }
        }
      }
      
      // 发送完整响应
      res.write(`data: ${JSON.stringify({ 
        choices: [{ message: { content: completeResponse } }],
        partial: false
      })}\n\n`);
      
      // 发送结束标记
      res.write('data: [DONE]\n\n');
      
    } catch (error) {
      console.error('AI服务调用失败:', error);
      
      // 发送错误信息
      res.write(`data: ${JSON.stringify({ 
        error: '服务器内部错误',
        choices: [{ message: { content: '抱歉，我现在无法回答这个问题。' } }]
      })}\n\n`);
      
      // 发送结束标记
      res.write('data: [DONE]\n\n');
    }
  }
} 