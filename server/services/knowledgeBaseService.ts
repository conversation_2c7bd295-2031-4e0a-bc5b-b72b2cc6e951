import axios from 'axios';
import { ConfigService } from './configService';
import FormData from 'form-data';
import { Blob } from 'buffer';

export class KnowledgeBaseService {
  private static instance: KnowledgeBaseService;
  private configService: ConfigService;

  private constructor() {
    this.configService = ConfigService.getInstance();
  }

  public static getInstance(): KnowledgeBaseService {
    if (!KnowledgeBaseService.instance) {
      KnowledgeBaseService.instance = new KnowledgeBaseService();
    }
    return KnowledgeBaseService.instance;
  }

  private async getConfig() {
    const config = await this.configService.getConfig();
    if (!config.knowledgeBaseEnabled || !config.knowledgeBaseApiKey || !config.knowledgeBaseApiUrl) {
      throw new Error('知识库功能未启用或配置不完整');
    }
    return config;
  }

  private async getAuthHeaders() {
    const config = await this.configService.getConfig();
    return {
      'Authorization': `Bearer ${config.knowledgeBaseApiKey}`,
      'Content-Type': 'application/json'
    };
  }

  /**
   * 获取知识库列表
   */
  public async getDatasets(page: number = 1, limit: number = 20) {
    try {
      const config = await this.getConfig();
      console.log('正在请求知识库API:', `${config.knowledgeBaseApiUrl}/v1/datasets`, {
        params: { page, limit }
      });

      const response = await axios.get(`${config.knowledgeBaseApiUrl}/v1/datasets`, {
        params: { page, limit },
        headers: await this.getAuthHeaders()
      });
      
      console.log('知识库API响应状态:', response.status);
      return response.data;
    } catch (error: any) {
      console.error('获取知识库列表失败:', error.message);
      if (error.response) {
        console.error('API响应详情:', {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers
        });
      } else if (error.request) {
        console.error('未收到API响应，请求详情:', error.request);
      }
      throw new Error(`获取知识库列表失败: ${error.message}`);
    }
  }

  /**
   * 创建知识库
   */
  public async createDataset(data: any) {
    try {
      const config = await this.getConfig();
      const response = await axios.post(`${config.knowledgeBaseApiUrl}/v1/datasets`, data, {
        headers: await this.getAuthHeaders()
      });
      
      return response.data;
    } catch (error: any) {
      console.error('创建知识库失败:', error);
      throw new Error(`创建知识库失败: ${error.message}`);
    }
  }

  /**
   * 删除知识库
   */
  public async deleteDataset(datasetId: string) {
    try {
      const config = await this.getConfig();
      const response = await axios.delete(`${config.knowledgeBaseApiUrl}/v1/datasets/${datasetId}`, {
        headers: await this.getAuthHeaders()
      });
      
      return response.status;
    } catch (error: any) {
      console.error('删除知识库失败:', error);
      throw new Error(`删除知识库失败: ${error.message}`);
    }
  }

  /**
   * 获取文档列表
   */
  public async getDocuments(datasetId: string, page: number = 1, limit: number = 20) {
    try {
      const config = await this.getConfig();
      const response = await axios.get(`${config.knowledgeBaseApiUrl}/v1/datasets/${datasetId}/documents`, {
        params: { page, limit },
        headers: await this.getAuthHeaders()
      });
      
      return response.data;
    } catch (error: any) {
      console.error('获取文档列表失败:', error);
      throw new Error(`获取文档列表失败: ${error.message}`);
    }
  }

  /**
   * 通过文本创建文档
   */
  public async createDocumentByText(datasetId: string, data: any) {
    try {
      const config = await this.getConfig();
      const response = await axios.post(
        `${config.knowledgeBaseApiUrl}/v1/datasets/${datasetId}/document/create_by_text`,
        data,
        {
          headers: await this.getAuthHeaders()
        }
      );
      
      return response.data;
    } catch (error: any) {
      console.error('创建文档失败:', error);
      throw new Error(`创建文档失败: ${error.message}`);
    }
  }

  /**
   * 通过文件创建文档
   */
  public async createDocumentByFile(datasetId: string, file: Express.Multer.File, data: string) {
    try {
      const config = await this.getConfig();
      
      // 确保文件名编码正确
      // 修正文件名编码：Multer 以 latin1 编码接收文件名，需要转换为 utf8
      const originalFileName = Buffer.from(file.originalname, 'latin1').toString('utf8');
      // 记录原始文件名，用于调试
      console.log('上传文件原始名称:', originalFileName);
      
      const formData = new FormData();
      
      // 直接用 Buffer，不用 Blob，兼容性更强
      formData.append('file', file.buffer, {
        filename: originalFileName,            // 明确指定文件名
        contentType: file.mimetype
      });
      
      formData.append('data', data);

      // 记录请求信息
      console.log('发送知识库文档请求:', {
        url: `${config.knowledgeBaseApiUrl}/v1/datasets/${datasetId}/document/create_by_file`,
        fileName: originalFileName,
        fileSize: file.size,
        mimeType: file.mimetype
      });

      const headers = await this.getAuthHeaders();
      const response = await axios.post(
        `${config.knowledgeBaseApiUrl}/v1/datasets/${datasetId}/document/create_by_file`,
        formData,
        {
          headers: {
            ...headers,
            'Content-Type': 'multipart/form-data'
          }
        }
      );
      
      console.log('知识库文档上传响应:', response.status, response.data);
      return response.data;
    } catch (error: any) {
      console.error('上传文件失败:', error);
      throw new Error(`上传文件失败: ${error.message}`);
    }
  }

  /**
   * 删除文档
   */
  public async deleteDocument(datasetId: string, documentId: string) {
    try {
      const config = await this.getConfig();
      const response = await axios.delete(
        `${config.knowledgeBaseApiUrl}/v1/datasets/${datasetId}/documents/${documentId}`,
        {
          headers: await this.getAuthHeaders()
        }
      );
      
      return response.data;
    } catch (error: any) {
      console.error('删除文档失败:', error);
      throw new Error(`删除文档失败: ${error.message}`);
    }
  }

  /**
   * 获取文档分段
   */
  public async getSegments(datasetId: string, documentId: string) {
    try {
      const config = await this.getConfig();
      const response = await axios.get(
        `${config.knowledgeBaseApiUrl}/v1/datasets/${datasetId}/documents/${documentId}/segments`,
        {
          headers: await this.getAuthHeaders()
        }
      );
      
      return response.data;
    } catch (error: any) {
      console.error('获取文档分段失败:', error);
      throw new Error(`获取文档分段失败: ${error.message}`);
    }
  }

  /**
   * 添加分段
   */
  public async createSegment(datasetId: string, documentId: string, data: any) {
    try {
      const config = await this.getConfig();
      const response = await axios.post(
        `${config.knowledgeBaseApiUrl}/v1/datasets/${datasetId}/documents/${documentId}/segments`,
        data,
        {
          headers: await this.getAuthHeaders()
        }
      );
      
      return response.data;
    } catch (error: any) {
      console.error('添加分段失败:', error);
      throw new Error(`添加分段失败: ${error.message}`);
    }
  }

  /**
   * 删除分段
   */
  public async deleteSegment(datasetId: string, documentId: string, segmentId: string) {
    try {
      const config = await this.getConfig();
      const response = await axios.delete(
        `${config.knowledgeBaseApiUrl}/v1/datasets/${datasetId}/documents/${documentId}/segments/${segmentId}`,
        {
          headers: await this.getAuthHeaders()
        }
      );
      
      return response.data;
    } catch (error: any) {
      console.error('删除分段失败:', error);
      throw new Error(`删除分段失败: ${error.message}`);
    }
  }

  /**
   * 更新分段
   */
  public async updateSegment(datasetId: string, documentId: string, segmentId: string, data: any) {
    try {
      const config = await this.getConfig();
      const response = await axios.post(
        `${config.knowledgeBaseApiUrl}/v1/datasets/${datasetId}/documents/${documentId}/segments/${segmentId}`,
        data,
        {
          headers: await this.getAuthHeaders()
        }
      );
      
      return response.data;
    } catch (error: any) {
      console.error('更新分段失败:', error);
      throw new Error(`更新分段失败: ${error.message}`);
    }
  }

  /**
   * 获取文档嵌入状态（进度）
   */
  public async getIndexingStatus(datasetId: string, batch: string) {
    try {
      const config = await this.getConfig();
      const response = await axios.get(
        `${config.knowledgeBaseApiUrl}/v1/datasets/${datasetId}/documents/${batch}/indexing-status`,
        {
          headers: await this.getAuthHeaders()
        }
      );
      
      return response.data;
    } catch (error: any) {
      console.error('获取文档嵌入状态失败:', error);
      throw new Error(`获取文档嵌入状态失败: ${error.message}`);
    }
  }
} 