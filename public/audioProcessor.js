/**
 * 音频处理工作线程
 * 替代已弃用的ScriptProcessorNode，处理音频数据并转换为16位PCM格式
 */
class AudioProcessor extends AudioWorkletProcessor {
  constructor(options) {
    super();
    
    // 处理状态
    this.isProcessing = false;
    
    // 获取配置的缓冲区大小，默认4096
    const processorOptions = options.processorOptions || {};
    this.bufferSize = processorOptions.bufferSize || 4096;
    
    // 监听主线程消息
    this.port.onmessage = (event) => {
      if (event.data.command === 'start') {
        this.isProcessing = true;
      } else if (event.data.command === 'stop') {
        this.isProcessing = false;
      }
    };
  }

  process(inputs, outputs) {
    // 如果未启动处理或没有输入，继续保持处理器运行但不处理数据
    if (!this.isProcessing || !inputs[0] || !inputs[0][0]) {
      return true;
    }

    // 获取输入数据
    const inputData = inputs[0][0];
    
    // 转换为16位整数
    const pcmBuffer = new ArrayBuffer(inputData.length * 2);
    const view = new DataView(pcmBuffer);
    
    for (let i = 0; i < inputData.length; i++) {
      // 将浮点数转换为16位整数
      const s = Math.max(-1, Math.min(1, inputData[i]));
      view.setInt16(i * 2, s < 0 ? s * 0x8000 : s * 0x7FFF, true);
    }
    
    // 发送处理后的音频数据到主线程
    this.port.postMessage({
      audioBuffer: pcmBuffer
    });

    // 返回true以保持处理器运行
    return true;
  }
}

// 注册处理器
registerProcessor('audio-processor', AudioProcessor); 