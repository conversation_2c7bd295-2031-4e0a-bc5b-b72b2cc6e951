## 1. SDK运行原理

![SDK运行原理.png](https://bce.bdstatic.com/doc/bce-doc/AI_DH/SDK%E8%BF%90%E8%A1%8C%E5%8E%9F%E7%90%86_a3b0ca7.png)

<br>

## 2. 接入方法

iframe支持在网页内引入其他网页，使用iframe引入WebSdk方式如下：

**FAQ: 为什么使用iframe**

使用iframe的方式天然的和集成放代码形成隔离，形成单独的进程进行数字人的渲染工作，防范因js单线程设计可能造成的渲染卡顿问题。

```js
// 用户侧
<iframe
    id="myIframe"
    src="https://open.xiling.baidu.com/cloud/realtime?token=自行填写&initMode=noAudio&cameraId=0
    frameBorder="0"
    allow="microphone;camera;midi;encrypted-media;autoplay;" // 可用权限
/>
```

<br>

## 3. demo使用

### 3.1 demo说明

此demo分别使用react，vue两种框架。针对如何发送消息驱动数字人，如何监听驱动数字人消息的完成状态，如何第一时间判断数字人已加载完成

如何解决浏览器静音策略，以及如何节省数字人资源等问题，进行了代码实现。

### 3.2 demo下载

React版本，下载地址https://digital-human-js-cdn.bj.bcebos.com/realtime-digital-human-demo.zip

Vue版本，下载地址https://digital-human-js-cdn.bj.bcebos.com/realtime-digital-human-vue-demo.zip

核心代码如下

```js
// 参考文献 https://developer.mozilla.org/en-US/docs/Web/Media/Autoplay_guide
import { useEffect, useState } from 'react'
import {v4 as uuidV4} from 'uuid';

import DHApp from './DHApp'

import './App.css'

enum ReadyState {
  UNINSTANTIATED = -1,
  CONNECTING = 0,
  OPEN = 1,
  CLOSING = 2,
  CLOSED = 3,
}

// 可以有声播放则返回true,否则返回false
function checkPlayUnMute(): Promise<boolean> {
  return new Promise((resolve) => {
    const audioElem: HTMLAudioElement = document.createElement('audio');
    audioElem.src = 'data:audio/wav;base64,UklGRigAAABXQVZFZm10IBAAAAABAAEAESsAACJWAAACABAAZGF0YQAAAAA=';
    audioElem.muted = false;

    const playPromise: Promise<void> | undefined = audioElem.play();

    if (playPromise !== undefined) {
      playPromise
        .then(() => {
          // 自动播放成功
          resolve(true);
        })
        .catch((error: Error) => {
          if (error.name === "NotAllowedError" || error.name === "AbortError") {
            // 自动播放被禁止或中止
            resolve(false);
          } else {
            // 其他错误
            resolve(false);
          }
        });
        audioElem.remove();
    } else {
      // 如果 play() 返回 undefined，可能是浏览器不支持 Promise 风格的 play()
      audioElem.remove();
      resolve(false);
    }
  });
}


function App() {
  // 实时视频流远端准备完毕，可以进行textRender等驱动指令的发送
  const [realTimeVideoReady, setRealTimeVideoReady] = useState(false);
  const [wsConnected, setWsConnected] = useState(false);
  // 浏览器限制导致的有声视频无法自动播放,所以这里进行静音后自动播放
  const [videoIsMuted, setVideoIsMuted] = useState(false);
  const [commandId, setCommandId] = useState(uuidV4());
  // 是否可以自动播放，检测完成状态
  const [checkOver, setCheckOver] = useState(false);
  // 超时即将消失tip
  const [showTimeoutTip, setShowTimeoutTip] = useState(false);
  // 数字人前置loading页面
  const [showLoadingPage, setShowLoadingPage] = useState(false);

  const onMessage = (msg: any) => {
    if (msg.origin === 'https://open.xiling.baidu.com') {
      const {type, content} = msg.data;
      switch (type) {
        case 'rtcState':
          if (content.action === 'remoteVideoConnected') {
            setRealTimeVideoReady(true);
          }
          if (content.action === 'localVideoMuted' && content.body) {
            setVideoIsMuted(true);
          }
          break;
        case 'wsState':
          if (content.readyState === ReadyState.OPEN) {
            setWsConnected(true);
          }
          else if (content.readyState === ReadyState.CLOSED || content.readyState === ReadyState.CLOSING) {
            setWsConnected(false);
          }
          break;
        case 'msg':
          const {action, requestId} = content;
          if (requestId === commandId && action === 'RENDER_COMPLETED') {
            console.info(`数字人驱动完成, 驱动id为${requestId}`);
            const newCommandId = uuidV4();
            // 解开注释可以进行持续播报
            // setCommandId(newCommandId); 
            DHApp.sendMessage({
              action: 'TEXT_RENDER',
              body: '滴滴滴，我已经完成一次驱动，这是我说的第n句话',
              requestId: newCommandId
            });
          }
          else if (action === 'DISCONNECT_ALERT') {
            setShowTimeoutTip(true);
          }
          else if (action === 'TIMEOUT_EXIT') {
            const iframeDom = document.getElementById('digital-human-iframe');
            iframeDom?.remove();
          }
          break;
        default:
          break;
      }
    }
  };

  const playWelcome = () => {
    setVideoIsMuted(false);
    DHApp.sendCommand({
      subType: 'muteAudio',
      subContent: false
    });
    DHApp.sendMessage({
      action: 'TEXT_RENDER',
      body: '滴滴滴，哈哈哈哈，这是我的开场白自我介绍，我是数字人',
      requestId: commandId
    });
  }

  const handleInterrupt = () => {
    DHApp.sendMessage({
      action: 'TEXT_RENDER',
      body: '<interrupt></interrupt>',
      requestId: commandId
    });
  }

  const goNextPage = () => {
    setShowLoadingPage(false);
    setVideoIsMuted(false);
  }

  useEffect(() => {
    const checkPlayUnMuteFun = async () => {
      let result = await checkPlayUnMute(); // 500ms左右
      console.log('result', result);
      setVideoIsMuted(!result as boolean);
      setCheckOver(true);
    }
    checkPlayUnMuteFun();
  }, [])

  useEffect(() => {
    console.log('checkOver', checkOver, 'videoIsMuted', videoIsMuted);
    if (realTimeVideoReady && wsConnected && checkOver && !videoIsMuted) {
      DHApp.sendMessage({
        action: 'TEXT_RENDER',
        body: '滴滴滴，哈哈哈哈，这是我的开场白自我介绍，我是数字人',
        requestId: commandId
      });
  }
  }, [realTimeVideoReady, wsConnected, checkOver]);

  useEffect(() => {
    DHApp.registerMessageReceived(onMessage);
    return () => {
      DHApp.removeMessageReceived(onMessage);
    };
  }, [onMessage]);

  if (showLoadingPage) {
    return (
      <div onClick={goNextPage}>
        前置loading页，点击进入下个页面
      </div>
    )
  }


  return (
    <>
      {videoIsMuted && <div className="tip1" onClick={playWelcome}>请点击这里的文字, 取消静音</div>}
      {showTimeoutTip && <div className="tip2" onClick={playWelcome}>长时间无交互，数字人马上消失了，点我一下，进行交互</div>}
      <div onClick={handleInterrupt}>打断播报</div>
      <div onClick={playWelcome}>驱动播报</div>
      <iframe
        id="digital-human-iframe"
        src="https://open.xiling.baidu.com/cloud/react?appId=i-pf4wvru84iyzpyzyt&appKey=fyexucnbpd5x8emdgnnvyzyt&&ttsPer=5116&initMode=noAudio&cameraId=1&resolutionWidth=1080&resolutionHeight=1920&cp-inactiveDisconnectSec=70&cp-preAlertSec=10&videoBg=rgb(242,245,249)&cp-autoAnimoji=true&ttsPitch=5&ttsSpeed=5&ttsVolume=5&backgroundImageUrl=https%3A%2F%2Fagi-dev-platform-web.cdn.bcebos.com%2Fai_apaas%2Fdist%2Fimg%2Fbackground_b46ba146.png"
        width="630"
        height="1120"
        allow="autoplay"
      >
      </iframe>
    </>
  )
}

export default App
```

### 3.3 demo运行

1. 命令行执行yarn，安装所需依赖包
2. 命令行执行npm run dev，启动项目
3. 将控制台输出的ip地址，复制到浏览器地址栏进行项目预览

<br>

### 3.4 demo修改

1. 平台购买所需组件[平台操作指南](https://cloud.baidu.com/doc/AI_DH/s/plyy6xhi0)
2. 查找文档[形象信息列表](https://cloud.baidu.com/doc/AI_DH/s/mlywga55u)，填写对应 figureId 
3. 根据[接口通用说明](https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/53G-5UwZAd/2WOdB-xdkN/K1sqlbEtmzoL17)生成鉴权参数 token
4. 删除 demo中的 appKey, appId

<br>

## 4. url参数说明

### 4.1 常用基础参数

| 参数             | 类型   | 必填 | 默认                     | 备注                                                         |
| :----------------: | :------: | :----: | ------------------------ | ------------------------------------------------------------ |
| token            | string | 是   | -                        | 平台购买所需组件[平台操作指南](https://cloud.baidu.com/doc/AI_DH/s/plyy6xhi0),生成鉴权凭证:生成方法查阅[接口通用说明](AI_DH/视频组件使用/接口通用说明.md)(token=Authorization)的鉴权参数生成部分 |
| figureId         | string | -    | -                        | 查找文档[形象信息列表](https://cloud.baidu.com/doc/AI_DH/s/mlywga55u)，填写对应 figureId                                                  |
| initMode         | String | 是   | -                        | 用于针对特定应用场景快速批量设置关联配置的默认值，设置后关联配置无需填写，枚举类型：**noAudio**：无拾音模式 |
| resolutionWidth  | string |      | 当前设备宽度clientWidth  | 视频分辨率宽，只能是偶数，最小值400，分辨率最大不超过 1080x1920、1920x1080 |
| resolutionHeight | string |      | 当前设备高度clientHeight | 视频分辨率高，只能是偶数，最小值400，分辨率最大不超过 1080x1920、1920x1080 |
| cameraId         | int    |      | -                        | 数字人预设机位 ID，控制数字人的位置和大小，建议使用和视频分辨率比例一致的机位：高精 2D 人像：0 （横屏半身）、1（竖屏半身）详细枚举参考[3D数字人](AI_DH/数字人形象使用/公共形象库/3D数字人.md)、[2D精品数字人](AI_DH/数字人形象使用/公共形象库/2D精品数字人.md) |
<br>

### 4.2 高级参数

| 参数                     | 类型    | 默认   | 备注                                                         |
| :------------------------: | :-------: | :------: | ------------------------------------------------------------ |
| mode                     | string  | inline | 画面裁剪参数（inline内嵌展示未填充的地方背景色展示 、crop裁剪） |
| videoBg                  | string  | rgb()  | 设置页面背景，默认黑色，需要用rgb的形式去使用                |
| backgroundImageUrl       | string  | -      | 设置数字人video背景图片，暂不支持直接配置颜色                |
| ttsPer                   | string  | -      | 音色                                                         |
| ttsPitch                 | number  | 5      | 音调                                                         |
| ttsSpeed                 | number  | 5      | 音速                                                         |
| ttsVolume                | number  | 5      | 音量                                                         |
| cp-autoAnimoji           | Boolean | false  | 是否开启自动添加数字人动作，只支持高精3D/2D人像              |
| cp-inactiveDisconnectSec | int     | 0      | 长时间无交互断连，单位秒，0 表示不开启。超时服务端会发送 TIMEOUT_EXIT 消息，父页面在收到该消息时需销毁子页面。 在发送销毁消息前，服务端会发送提醒消息，参考 cp-preAlertSec 参数说明。 |
| cp-preAlertSec           | int     | 60     | 长时间无交互提醒断连前后端会发送提醒消息。<br> DISCONNECT_ALERT，父页面可以弹窗提醒用户。该参数用于设置断连前多少秒发送该提醒，比如超时参数设置为 3 分钟，提醒参数设置为 1 分钟，则在用户最后一次交互 2 分钟后发送提醒消息，在用户最后一次交互 3 分钟后发送断连消息。 |
| logoUrl                  | string  | -      | 自定义logo图片地址                                           |
| entry                    | Boolean | false  | 是否有入口页（开启后不会自动播放）<br>一般用于手机端iframe集成，手机端无法自动播放 |
<br>

### 4.3 debug参数设置

| 参数        | 类型    | 默认  | 备注                                                         |
| ----------- | ------- | ----- | ------------------------------------------------------------ |
| appId       | string  | -     | 应用标识和秘钥，用于鉴权，前端直接使用可能会有泄漏风险，不建议生产环境使用，推荐使用 token 鉴权。 |
| appKey      | string  | -     |                                                              |
| showMessage | Boolean | false | 展示toast提示消息                                            |
| textAssist  | Boolean | false | 是否展示文本辅助, 可以进行驱动                               |
| debug       | Boolean | false | 展示vconsole                                                 |
<br>
<br>

## 5. 如何与Web-Sdk通信

使用iframe的内外层window可以通过[window.postMessage](https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage)这个API发送消息，基于[window.postMessage](https://developer.mozilla.org/en-US/docs/Web/API/Window/postMessage)封装好的通信util——**DHApp.ts**，提供了收、发消息的方法，用户可直接使用。

### 5.1 DHApp.ts

```js
/**
 * @file DHIFrame
 * @desc 1、iframe父页面发送消息到子页面 2、iframe父页面接收子页面消息
 */

const MESSAGE_TYPE = {
    TEXT_RENDER: 'TEXT_RENDER'
};

export default {
    /**
     * 注册接收数据方法
     * * @param {(this: Window, ev: MessageEvent<any>) => any} callback 方法实现
     */
    registerMessageReceived(callback: (this: Window, ev: MessageEvent<any>) => any) {
        if (typeof window.addEventListener !== 'undefined') {
            window.addEventListener('message', callback, false);
        }
    },

    /**
     * 移除接收数据方法
     * @param {(this: Window, ev: MessageEvent<any>) => any} callback 方法实现
     */
    removeMessageReceived(callback: (this: Window, ev: MessageEvent<any>) => any) {
        if (typeof window.addEventListener !== 'undefined') {
            window.removeEventListener('message', callback, false);
        }
    },

    /**
     * 发送消息到iframe - 基础方法
     * @param {string} type 消息类型
     * @param {any} content 消息内容
     */
    sendMessageToIframe(type: string, content: any) {
        // 业务里面的iframe
        const iframeEle: any | null = document.getElementById('myIframe') as HTMLIFrameElement;
        if (type && iframeEle !== null) {
            iframeEle.contentWindow.postMessage({type, content}, '*');
        }
    },

    /**
     * 发送 command 消息到 iframe
     * @param {any} message 消息
     */
    sendCommand(command: any) {
        this.sendMessageToIframe('command', command);
    },

    /**
     * 发送 message 消息到 iframe
     * @param {any} message 消息
     */
    sendMessage(message: any) {
        this.sendMessageToIframe('message', message);
    },

    /**
     * 文本驱动数字人
     * @param {any} message 消息
     */
    textRender(message: any) {
        this.sendMessage({
            action: MESSAGE_TYPE.TEXT_RENDER,
            body: message
        });
    },

    /**
     * 静音audio元素声音
     * @param {boolean} mute
     */
    muteAudio(mute: boolean) {
        this.sendCommand({
            subType: 'muteAudio',
            subContent: mute
        });
    },

    /**
     * 播放
     * @param {boolean} mute
     */
    playVideo(play: boolean) {
        this.sendCommand({
            subType: 'playVideo',
            subContent: play
        });
    }
};
```

### 5.2 接收消息

用户侧可以通过如下方式接收leafletWeb抛出的消息

```js
import DHApp from 'DHApp';

const onMessage = msg => {
    console.log(msg.data);
    // {type, content} = msg.data;
};

// 增加监听
DHApp.registerMessageReceived(onMessage);
// 移除监听
DHApp.removeMessageReceived(onMessage);
```

#### 5.2.1 消息Type类型

用户侧能够接收到的消息（即Web-sdk抛出的消息）有三类，通过msg.data的type字段表示，三类消息说明如下：

| type     | content  | 备注                   |
| -------- | -------- | ---------------------- |
| msg      | msg      | 数字人驱动消息回调通知 |
| rtcState | rtcState | webrtc 的状态          |
| wsState  | wsState  | webSocket 的链接状态   |
<br>


#### 5.2.2 rtcState数据信息

type为 rtcState 的 **content** 的 格式 {action, body}

| action               | body  | 备注                                   |
| -------------------- | ----- | -------------------------------------- |
| success              | true  | rtc房间登录成功                        |
| error                | error | rtc房间登录失败                        |
| remotevideoloading   | id    | 远端视频流开始加载                     |
| remoteVideoConnected | id    | 成功获取远端视频流（不受视频暂停影响） |
| remotevideoon        | id    | 远端视频流加载完毕                     |
| localVideoMuted      | true  | 首次加载无交互导致的video静音          |
<br>


#### 5.2.3 wsState数据信息

type为 wsState 的 **content.readyState**

| 状态           | value | 备注          |
| -------------- | ----- | ------------- |
| UNINSTANTIATED | -1    | 默认值 无状态 |
| CONNECTING     | 0     | ws 连接中     |
| OPEN           | 1     | ws 开启成功   |
| CLOSING        | 2     | ws 关闭中     |
| CLOSED         | 3     | ws 关闭       |

```js
enum ReadyState {
  UNINSTANTIATED = -1,
  CONNECTING = 0,
  OPEN = 1,
  CLOSING = 2,
  CLOSED = 3,
}
```

#### 5.2.4 msg数据信息

type为 msg 的 **content.action**

- 类别：驱动数字人回调消息

| 状态             | 备注                         | 内容数据 |
| ---------------- | ---------------------------- | -------- |
| RENDER_START     | 数字人收到驱动后，驱动开始   | -        |
| RENDER_COMPLETED | 数字人完成驱动指令，驱动完成 | -        |
| RENDET_ERROR     | 文本驱动错误                 | -        |
| DOWN_SUBTITLE    | 播报内容                     | body     |

- 类别：数字人退出回调消息

| 状态         | 备注         | 内容数据 |
| ------------ | ------------ | -------- |
| TIMEOUT_EXIT | 超时退出消息 |          |

- 类别：数字人初始化消息回调

| 状态             | 备注             | 内容数据 |
| ---------------- | ---------------- | -------- |
| CONNECT          | 连接消息         | body     |
| DISCONNECT_ALERT | 长时间无交互提示 | ~~~~     |

﻿

type为 msg 的 **content.code**

- 错误类型：connect连接，初始化数字人报错报错

| 错误码 | 错误信息                       | 说明                     |
| ------ | ------------------------------ | ------------------------ |
| 1001   | 服务器内部错误                 |                          |
| 1002   | APP已失效                      |                          |
| 1003   | APP token已过期                |                          |
| 1004   | 目前线上用户较多，请您稍后再试 | app 会话路数已经达到上限 |
| 1009   | App key非法                    |                          |
| 1011   | 裁剪参数非法                   | 仅2D数字人存在           |
| 1012   | 参数不能为空                   |                          |
| 1014   | 分辨率/编码参数不合法          |                          |
| 1015   | 无效的媒体信息                 | 例如：传递的RTMP参数无效 |
| 1202   | 正在连接或已连接               |                          |
| 1204   | 解析请求失败                   |                          |
| 1206   | 读取json失败                   |                          |
| 2001   | Fail to establish              | 建立渲染任务失败         |
| 3001   | 目前线上用户较多，请您稍后再试 | 系统渲染资源不足导致     |

- 错误类型：驱动数字人报错

| 错误码 | 错误信息       | 说明                                                         |
| ------ | -------------- | ------------------------------------------------------------ |
| 1013   | DRML非法       | 仅2D数字人存在，不支持做该动作同时做播报，通常是走入走出的情况，面部不完整 |
| 1208   | 富文本格式非法 | DRML格式错误                                                 |
| 1201   | 操作尚未支持   |                                                              |
<br>
<br>

### 5.3 发送消息

用户侧一般需要发送的消息类型有两类。

一类是发给sdk对应后端服务的消息，如驱动或查询数字人；

另一个是指令型消息，如音频静音、视频播放等。

#### 5.3.1 sendMessage方式

DHApp针对不同消息类型分别封装了不同的方法，可直接使用。

代码实例

```js
import {v4 as uuidV4} from 'uuid';
// data 格式类型
const jsonStr = {action: 'TEXT_RENDER', body: '你好', requestId: uuidV4()}; // requestId不加默认使用sdk自生成的id
// 发送消息
DHApp.sendMessage(jsonStr);
```

**requestId**说明：可自定义构造requestId进行驱动数字人，驱动的回调消息会沿用自定义构造的requestId。

**action**说明：可选值：TEXT_RENDER，含义为进行数字人语音驱动

**body**说明：

| TEXT_RENDER时body的可选值                                    | 备注                                                         |
| ------------------------------------------------------------ | ------------------------------------------------------------ |
| 你好，我是数字人                                             | 播报内容                                                     |
| &lt;interrupt&gt;&lt;/interrupt&gt;                                      | 打断当前数字人播报                                           |
| &lt;speak&gt;`<say-as type="telephone">110</say-as>`&lt;/speak>       | 指定读法的高级播报内容，支持[在线SSML（语音合成标记语言）](AI_DH/视频组件使用/SSML使用说明.md) |
| &lt;speak&gt;开始说话&lt;silence time="5s"&gt;&lt;/silence&gt;停止5s后继续说话&lt;/speak&gt; | 支持播报中停顿                                               |
| &lt;speak&gt;&lt;audio src="http://meitron-test.bj.bcebos.com/mofachengbao_part.wav"/> &lt;/speak&gt; | 音频播放                                                     |
| &lt;speak interruptible="false"&gt;这段话不允许打断这段话不允许打断这段话不允许打断&lt;/speak&gt; | 播报不受打断的内容，后续textRender会在上句播报完成后开始播报，但仍可被interrupt标签打断 |

驱动消息回调事件，查看 【5.2.4 msg数据信息】

#### 5.3.2 sendCommand方式

**实例代码**

```js
// 驱使数字人视频静音指令
DHapp.sendCommand({
    subType: 'muteAudio',
    subContent: mute // mute: true/false
});
```

**subType参数**

| subType可选值 | 说明                     | DHApp中已封装好的方法 | 使用方式              |
| ------------- | ------------------------ | --------------------- | --------------------- |
| mute          | 本地麦克风拾音，静音     | mute                  | DHApp.mute(true)      |
| muteAudio     | 静音本地vidio, audio元素 | muteAudio             | DHApp.muteAudio(true) |
| playVideo     | 数字人视频被暂停时播放   | playVideo             | DHApp.playVideo(true) |
<br>
<br>

## 6. 常见问题

### 6.1 拾音能力依赖HTTPS（initMode = noAudio情况下无需关注）

采集音频使用的js能力getUserMedia，该api需要依赖https才可以开启

### 6.2 部分浏览器视频组件层级过高

移动端部分android浏览器对视频组件进行过特殊处理，导致视频组件悬浮于页面之上，该场景无法通过代码去解决

1. 如果只是层级过高，并不会直接全屏，那么可以使用半屏数字人或数字人头像大小，不在数字人界面上方叠加组件
2. 如果视频组件层级高且会直接全屏，那么可以直接拦截当前浏览器对数字人的使用

### 6.3 移动端集成，无法自动播放问题；（实现详见结尾demo）

移动端受限于浏览器策略，无法自动播放，需要一次用户交互行为（比如点击），常见处理方案

1. 进入数字人前有个引导页，页面上进行业务介绍与开始业务的按钮，点击按钮触发数字人的播放
2. 提前加载数字人iframe，然后将播放事件绑定在前置流程中的按钮上

```plain
相关配置：
entry=true

按钮触发方法：
DHApp.playVideo(true);
```

> 注意：部分浏览器只是会继承上一次的用户交互时间，一段时间后也会失效，如果为了保障数字人的稳定性，建议前置加按钮触发

### 6.4 驱动数字人进行开场白话术，只有唇动无声音 （实现详见篇首demo）

移动端受限于浏览器策略，无法自动有声音视频播放，sdk侧逻辑进行静音播放，

这时需要集成侧，引导用户完成交互操作后，使用DHapp.sendCommand({ subType: 'muteAudio', subContent: true });进行取消静音

### 6.5 驱动数字人后，怎么中途打断数字人播报，以及如何监听数字人播报完成（实现详见篇首demo）

打断发送消息 DHApp.sendMessage( {action: 'TEXT_RENDER', body: '<interrupt></interrupt>');

监听播放完成，首先指定TEXT_RENDER的requestId, 然后监听指定requestId的RENDER_COMPLETED

### 6.6 数字人路数较少，如何节约资源，尽可能让资源在客户不用的时候尽快释放掉 （实现详见篇首demo）

设置cp-inactiveDisconnectSec， cp-preAlertSec字段

并监听DISCONNECT_ALERT进行适当的交互提示

监听TIMEOUT_EXIT消息进行销毁iframe操作

### 6.7 拉流没有画面

1. 请检查当前页面协议是否为HTTPS，若不是，请更换成HTTPS；
2. 请按照下图所示步骤继续排查客户端是否收到流

![拉流没有画面.png](https://bce.bdstatic.com/doc/bce-doc/AI_DH/%E6%8B%89%E6%B5%81%E6%B2%A1%E6%9C%89%E7%94%BB%E9%9D%A2_a87e1c0.png)

1. tab页开启h5-sdk保证已经拉流完成
2. 新开tab页输入chrome://webrtc-internals/
3. 点击列表中的最后一个链接

![点击列表中的最后一个链接.png](https://bce.bdstatic.com/doc/bce-doc/AI_DH/%E7%82%B9%E5%87%BB%E5%88%97%E8%A1%A8%E4%B8%AD%E7%9A%84%E6%9C%80%E5%90%8E%E4%B8%80%E4%B8%AA%E9%93%BE%E6%8E%A5_ef63e10.png)

1. 页面全局搜索bytesReceived, 一直键入回车，直到找到对应页面的[bytesReceived_in_bits/s]图表，检查图标是否为空
2. 若该图表为空，可能是浏览器插件导致，为过滤浏览器插件影响，请执行以下操作之一：
   1. 关闭所有浏览器插件，重新打开页面
   2. 或打开chrome浏览器无痕窗口，重新打开页面
   3. 或更换其他浏览器，重新打开页面

目前已知会影响RTC正常拉流的浏览器插件：**PureVPN Proxy**；

如关闭所有浏览器插件并更换其他浏览器仍没有画面，请提交工单与我们联系；